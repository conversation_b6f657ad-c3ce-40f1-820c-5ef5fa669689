{"__meta": {"id": "01JZ50TP815Q4ESRPK3X1ZE22Z", "datetime": "2025-07-02 07:32:41", "utime": **********.859049, "method": "GET", "uri": "/api/2024-12/organization/billing/status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.080464, "end": **********.859087, "duration": 0.778623104095459, "duration_str": "779ms", "measures": [{"label": "Booting", "start": **********.080464, "relative_start": 0, "end": **********.759212, "relative_end": **********.759212, "duration": 0.****************, "duration_str": "679ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.759238, "relative_start": 0.****************, "end": **********.85909, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "99.85ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.778964, "relative_start": 0.****************, "end": **********.785052, "relative_end": **********.785052, "duration": 0.0060880184173583984, "duration_str": "6.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.852555, "relative_start": 0.****************, "end": **********.852775, "relative_end": **********.852775, "duration": 0.0002200603485107422, "duration_str": "220μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.855124, "relative_start": 0.****************, "end": **********.855243, "relative_end": **********.855243, "duration": 0.00011897087097167969, "duration_str": "119μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization/billing/status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "as": "organization.billing.status", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.006189999999999999, "accumulated_duration_str": "6.19ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.810487, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 90.307}, {"sql": "select * from `organizations` where `id` is null and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.8419049, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:177", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=177", "ajax": false, "filename": "OrganizationController.php", "line": "177"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.307, "width_percent": 9.693}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization/billing/status\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null"}, "request": {"data": {"status": "404 Not Found", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus", "uri": "GET api/2024-12/organization/billing/status", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4aed36-cfc4-42c9-888d-d382d3db6efc\" target=\"_blank\">View in Telescope</a>", "duration": "787ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1860463282 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1860463282\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1230540622 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1230540622\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-618223023 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImJGSnNlbHN2QTZ5ajJIU1lzYmNMeFE9PSIsInZhbHVlIjoib1BUV2U0a3VTc2grVUFoc0NpTUNaTGtYbGpxSHhLdm9IaUdiU1FiSWdCOG51bWszbjgwalFPS3RTNkdtWm1sNzE1Umt0eHBoUVk5V1BMbCtpaWZPbXQ2ckFpTVA3ci9IYysxRlppNFJ4NjhLVFF6bUFHU2d3V2xTVTlUOGs0STIiLCJtYWMiOiJkMzY0ZWIyMDNhYWUzZWJjNjYwNGU3MTRmMDI3NjM0YzBhNjY4ODI2YTY5MGRlOTcwNDQ0YzJjMThiOWI2YTMwIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ik5pVkpjV0NpMGhtc0htckFIWGdiTXc9PSIsInZhbHVlIjoiZCtyWnhLRExsWk9HQTJ5Vm14K3premgxV29VWVZVaE8rYWFQTUViM2Qyd1BKUG9sQWV3Y2VleHJEZm1VZyt5OTRucW1wRTlsMlVGVnBEV3lrdDBRMmtiNktGQlJHUHpwemZVemhGREJudVBRN2ZjbENaaHZLNnRrZ0lwNnZIUDRPY2YvZzJLL3ZlaEY3bmxoWDNMT3l3PT0iLCJtYWMiOiI1MTkzYmI4N2FjMmQ4ODVkZThlNjUwZWM3NzIzNTZlOWY2ODRhYWRlZGFlYzFlOTQwZWI3MjMwM2Y3NjRhN2E0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImJGSnNlbHN2QTZ5ajJIU1lzYmNMeFE9PSIsInZhbHVlIjoib1BUV2U0a3VTc2grVUFoc0NpTUNaTGtYbGpxSHhLdm9IaUdiU1FiSWdCOG51bWszbjgwalFPS3RTNkdtWm1sNzE1Umt0eHBoUVk5V1BMbCtpaWZPbXQ2ckFpTVA3ci9IYysxRlppNFJ4NjhLVFF6bUFHU2d3V2xTVTlUOGs0STIiLCJtYWMiOiJkMzY0ZWIyMDNhYWUzZWJjNjYwNGU3MTRmMDI3NjM0YzBhNjY4ODI2YTY5MGRlOTcwNDQ0YzJjMThiOWI2YTMwIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImVqQWZ4K296VDhKb2FvK1haR1ZDTGc9PSIsInZhbHVlIjoiNS9KUHpSbjBPVUowOUYxZE5Ram1GSnd1RkNMSElqdG5RY0IvV00wWVovTFFIRzQ1WStTWVhjN093OGdSODBiWS9GZ0J6R1FoZGVZUW1tYzh3MW5tRlBwN21lQW9nYUF6OEtUSEV3eWhvWmhkWkVBS29yY2lDUklnUDRKVWUycEsiLCJtYWMiOiJjNjA4ZjM1MTM5NDY1ZDg1MjA3NDczMjVkMzgxY2RkNDE1YzdhZjE5M2ZkYzIzMTcyNzk1NTZhZGJmNDk1N2E2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-618223023\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-977756123 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|K2HGVeiI9jZtEvGBchSIGCAc9A1qXjjK7xXs5akOgWdg7x1OlA2dT78yiGRj|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">uzMucvvBrBUbisr6fsGDlH40F7AZFtuWRVnGzwKf</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-977756123\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-851711206 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:32:41 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">54</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjhlSjVHYUlZSjNUVWpvU0VIcDBNaHc9PSIsInZhbHVlIjoiMzJkRnBrd3NNUjBiQXNnL0RKVzVablhCdHpDRFF5UHdiTXBLZ3ZaWTVBdjJjdkpWYUVLUVhReEIxWE01ZFcxMVBzQ3ZyL0VtU0ZpMGo2Mnl1RTlmUndESDBzanQ4OW1CRU5pNzl1SEdXdEx6SFY0N1ZrblRPQnVQVmxuenlPbzQiLCJtYWMiOiI1ZmFmZDI3NjU3YjJkMzI4MzEwN2I2YWMxN2Q4YzdiYjgzM2QyNmFhNmExNmMwNzEwZjFhYjI4NGJiZWUwZWJmIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:32:41 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6InNNa0pTcHR4R1VXbjZ0RW9DMHlrZkE9PSIsInZhbHVlIjoiazh4VCtrbEtkWVdNbFRjS0tUN1lKSXBMT2pCM3NvZHZJMHMxZWRCWWNnS3hJRG9VeWtPSHI2Uk16aUt6Z0NVWCt0d3pKY1NaT05UM0FuYjFWNmJRbHE0V3BlOGd5elJnWkcrcS9Yb2thdTc5NTRybm1VaHJaS042czRpeWdtUU0iLCJtYWMiOiI2ZmY2MDQ1ZWYwNzI2OTEyMGMyYzczN2EwOWZhNmI3NWEzN2Y3OWI2NDU1ZDM1YTlmZTg0NjcwODg3NjRjMTI2IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:32:41 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjhlSjVHYUlZSjNUVWpvU0VIcDBNaHc9PSIsInZhbHVlIjoiMzJkRnBrd3NNUjBiQXNnL0RKVzVablhCdHpDRFF5UHdiTXBLZ3ZaWTVBdjJjdkpWYUVLUVhReEIxWE01ZFcxMVBzQ3ZyL0VtU0ZpMGo2Mnl1RTlmUndESDBzanQ4OW1CRU5pNzl1SEdXdEx6SFY0N1ZrblRPQnVQVmxuenlPbzQiLCJtYWMiOiI1ZmFmZDI3NjU3YjJkMzI4MzEwN2I2YWMxN2Q4YzdiYjgzM2QyNmFhNmExNmMwNzEwZjFhYjI4NGJiZWUwZWJmIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:32:41 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6InNNa0pTcHR4R1VXbjZ0RW9DMHlrZkE9PSIsInZhbHVlIjoiazh4VCtrbEtkWVdNbFRjS0tUN1lKSXBMT2pCM3NvZHZJMHMxZWRCWWNnS3hJRG9VeWtPSHI2Uk16aUt6Z0NVWCt0d3pKY1NaT05UM0FuYjFWNmJRbHE0V3BlOGd5elJnWkcrcS9Yb2thdTc5NTRybm1VaHJaS042czRpeWdtUU0iLCJtYWMiOiI2ZmY2MDQ1ZWYwNzI2OTEyMGMyYzczN2EwOWZhNmI3NWEzN2Y3OWI2NDU1ZDM1YTlmZTg0NjcwODg3NjRjMTI2IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:32:41 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-851711206\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-78952078 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8000/api/2024-12/organization/billing/status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-78952078\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "404 Not Found", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus"}, "badge": "404 Not Found"}}