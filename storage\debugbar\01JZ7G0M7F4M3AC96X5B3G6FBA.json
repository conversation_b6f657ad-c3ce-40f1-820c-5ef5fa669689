{"__meta": {"id": "01JZ7G0M7F4M3AC96X5B3G6FBA", "datetime": "2025-07-03 06:36:33", "utime": **********.903803, "method": "GET", "uri": "/products/4/edit/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[06:36:33] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.201987, "xdebug_link": null, "collector": "log"}, {"message": "[06:36:33] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Rules\\MaxRule.php on line 49", "message_html": null, "is_string": false, "label": "warning", "time": **********.586004, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751524592.497888, "end": **********.903826, "duration": 1.405937910079956, "duration_str": "1.41s", "measures": [{"label": "Booting", "start": 1751524592.497888, "relative_start": 0, "end": **********.066555, "relative_end": **********.066555, "duration": 0.***************, "duration_str": "569ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.066568, "relative_start": 0.****************, "end": **********.903828, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "837ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.083857, "relative_start": 0.****************, "end": **********.09891, "relative_end": **********.09891, "duration": 0.015053033828735352, "duration_str": "15.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.383416, "relative_start": 0.****************, "end": **********.901235, "relative_end": **********.901235, "duration": 0.****************, "duration_str": "518ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "1x products.add", "param_count": null, "params": [], "start": **********.385933, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.phpproducts.add", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fproducts%2Fadd.blade.php&line=1", "ajax": false, "filename": "add.blade.php", "line": "?"}, "render_count": 1, "name_original": "products.add"}, {"name": "1x components.products.edit-product-header", "param_count": null, "params": [], "start": **********.390697, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header.blade.phpcomponents.products.edit-product-header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header.blade.php&line=1", "ajax": false, "filename": "edit-product-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-header"}, {"name": "1x completeness::components.product-header", "param_count": null, "params": [], "start": **********.392805, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-header.blade.phpcompleteness::components.product-header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2Fviews%2Fcomponents%2Fproduct-header.blade.php&line=1", "ajax": false, "filename": "product-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "completeness::components.product-header"}, {"name": "1x completeness::components.product-score-progress-bar", "param_count": null, "params": [], "start": **********.397713, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-score-progress-bar.blade.phpcompleteness::components.product-score-progress-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2Fviews%2Fcomponents%2Fproduct-score-progress-bar.blade.php&line=1", "ajax": false, "filename": "product-score-progress-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "completeness::components.product-score-progress-bar"}, {"name": "1x components.products.edit-product-header-btns", "param_count": null, "params": [], "start": **********.409087, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-btns.blade.phpcomponents.products.edit-product-header-btns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header-btns.blade.php&line=1", "ajax": false, "filename": "edit-product-header-btns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-header-btns"}, {"name": "1x components.products.edit-product-header-navs", "param_count": null, "params": [], "start": **********.532392, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-navs.blade.phpcomponents.products.edit-product-header-navs", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header-navs.blade.php&line=1", "ajax": false, "filename": "edit-product-header-navs.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-header-navs"}, {"name": "6x components.products.edit-product-attribute-status-dot", "param_count": null, "params": [], "start": **********.548324, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-attribute-status-dot.blade.phpcomponents.products.edit-product-attribute-status-dot", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-attribute-status-dot.blade.php&line=1", "ajax": false, "filename": "edit-product-attribute-status-dot.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.products.edit-product-attribute-status-dot"}, {"name": "1x completeness::components.product-fields-scoring", "param_count": null, "params": [], "start": **********.603678, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-fields-scoring.blade.phpcompleteness::components.product-fields-scoring", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2Fviews%2Fcomponents%2Fproduct-fields-scoring.blade.php&line=1", "ajax": false, "filename": "product-fields-scoring.blade.php", "line": "?"}, "render_count": 1, "name_original": "completeness::components.product-fields-scoring"}, {"name": "1x components.products.edit-product-selection-widget", "param_count": null, "params": [], "start": **********.637067, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-selection-widget.blade.phpcomponents.products.edit-product-selection-widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-selection-widget.blade.php&line=1", "ajax": false, "filename": "edit-product-selection-widget.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-selection-widget"}, {"name": "1x components.products.edit-product-footer-btns", "param_count": null, "params": [], "start": **********.641767, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-footer-btns.blade.phpcomponents.products.edit-product-footer-btns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-footer-btns.blade.php&line=1", "ajax": false, "filename": "edit-product-footer-btns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-footer-btns"}, {"name": "1x components.products.edit-product-base-form", "param_count": null, "params": [], "start": **********.642345, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-base-form.blade.phpcomponents.products.edit-product-base-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-base-form.blade.php&line=1", "ajax": false, "filename": "edit-product-base-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-base-form"}, {"name": "2x components.assets.delete-modal", "param_count": null, "params": [], "start": **********.643697, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/assets/delete-modal.blade.phpcomponents.assets.delete-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fassets%2Fdelete-modal.blade.php&line=1", "ajax": false, "filename": "delete-modal.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.assets.delete-modal"}, {"name": "1x layouts.app_new", "param_count": null, "params": [], "start": **********.645285, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.phplayouts.app_new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=1", "ajax": false, "filename": "app_new.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app_new"}, {"name": "1x layouts.navs.product_sidebar", "param_count": null, "params": [], "start": **********.647337, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.phplayouts.navs.product_sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=1", "ajax": false, "filename": "product_sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navs.product_sidebar"}, {"name": "1x components.alerts.import-alert", "param_count": null, "params": [], "start": **********.885912, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/import-alert.blade.phpcomponents.alerts.import-alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fimport-alert.blade.php&line=1", "ajax": false, "filename": "import-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.import-alert"}, {"name": "1x components.alerts.upgrade-billing-modal", "param_count": null, "params": [], "start": **********.89544, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/upgrade-billing-modal.blade.phpcomponents.alerts.upgrade-billing-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fupgrade-billing-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-billing-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.upgrade-billing-modal"}]}, "route": {"uri": "GET products/{id}/edit/{version_id?}", "middleware": "web, check_session, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ProductController@edit<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "as": "products.edit", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:125-217</a>"}, "queries": {"count": 143, "nb_statements": 143, "nb_visible_statements": 143, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.1574500000000001, "accumulated_duration_str": "157ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.127773, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 2.248}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.14254, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 2.248, "width_percent": 0.521}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.155935, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 2.769, "width_percent": 1.677}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.166828, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.446, "width_percent": 0.368}, {"sql": "select * from `products` where `products`.`id` = '4' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["4", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.17984, "duration": 0.00239, "duration_str": "2.39ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.814, "width_percent": 1.518}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.186266, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 6.332, "width_percent": 0.483}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.195693, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 6.815, "width_percent": 0.445}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at`, `invite_product`.`id` as `pivotId` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.203404, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.259, "width_percent": 0.432}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.209534, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.691, "width_percent": 0.788}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.215204, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 8.479, "width_percent": 0.451}, {"sql": "select * from `variants` where `variants`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.220748, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 8.93, "width_percent": 0.356}, {"sql": "select `files`.*, `file_product`.`product_id` as `pivot_product_id`, `file_product`.`file_id` as `pivot_file_id`, `file_product`.`uploaded_for` as `pivot_uploaded_for`, `file_product`.`created_at` as `pivot_created_at`, `file_product`.`updated_at` as `pivot_updated_at` from `files` inner join `file_product` on `files`.`id` = `file_product`.`file_id` where `file_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2281418, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.285, "width_percent": 0.381}, {"sql": "select * from `inventories` where `variant_id` is null and `inventories`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.231665, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.667, "width_percent": 0.387}, {"sql": "select * from `families` where exists (select * from `attribute_family_product_versions` where `families`.`id` = `attribute_family_product_versions`.`family_id` and `product_id` = 4 and `version_id` = 1) order by `id` asc", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.235687, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.054, "width_percent": 0.432}, {"sql": "select `attributes`.*, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.242904, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.486, "width_percent": 0.426}, {"sql": "select * from `attribute_types` where `attribute_types`.`id` in (1, 3, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.247579, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.911, "width_percent": 0.324}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.250781, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 11.235, "width_percent": 0.292}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 1 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.253502, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 11.527, "width_percent": 0.451}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 3 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 3, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.260138, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 11.978, "width_percent": 0.413}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 4 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 4, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.263568, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 12.391, "width_percent": 0.495}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 5 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 5, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.267183, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 12.887, "width_percent": 0.432}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 6 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 6, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.27062, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.319, "width_percent": 0.451}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 7 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 7, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2769392, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.769, "width_percent": 0.514}, {"sql": "select * from `versions` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 140}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.28056, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:140", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=140", "ajax": false, "filename": "ProductController.php", "line": "140"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 14.284, "width_percent": 0.305}, {"sql": "select * from `versions` where `versions`.`id` = '1' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 153}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.283832, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:153", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=153", "ajax": false, "filename": "ProductController.php", "line": "153"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 14.589, "width_percent": 0.279}, {"sql": "select `id` from `product_version` where `version_id` = 1 and `product_id` = '4'", "type": "query", "params": [], "bindings": [1, "4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.286895, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:23", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=23", "ajax": false, "filename": "VersionScoreTrait.php", "line": "23"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 14.868, "width_percent": 0.311}, {"sql": "select `id` from `families` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.292607, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:32", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=32", "ajax": false, "filename": "VersionScoreTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.179, "width_percent": 0.349}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2963312, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:39", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=39", "ajax": false, "filename": "VersionScoreTrait.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.529, "width_percent": 0.305}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` = 4 and `value` is not null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.299644, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:43", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=43", "ajax": false, "filename": "VersionScoreTrait.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.834, "width_percent": 0.349}, {"sql": "select count(*) as aggregate from `brand_product` where `product_id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.303426, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:45", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=45", "ajax": false, "filename": "VersionScoreTrait.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.183, "width_percent": 0.286}, {"sql": "select count(*) as aggregate from `category_product` where `product_id` = '4' and `category_product`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.309589, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:46", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=46", "ajax": false, "filename": "VersionScoreTrait.php", "line": "46"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.469, "width_percent": 0.337}, {"sql": "select count(*) as aggregate from `product_vendor` where `product_id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.31308, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:47", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=47", "ajax": false, "filename": "VersionScoreTrait.php", "line": "47"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.805, "width_percent": 0.299}, {"sql": "select `id` from `product_version` where `product_id` = '4' and `version_id` = 1", "type": "query", "params": [], "bindings": ["4", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 168}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.31619, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:168", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=168", "ajax": false, "filename": "ProductController.php", "line": "168"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.104, "width_percent": 0.279}, {"sql": "select * from `product_version` where `product_id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1770}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.320034, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:1770", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1770}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1770", "ajax": false, "filename": "Product.php", "line": "1770"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.383, "width_percent": 0.324}, {"sql": "select * from `versions` where `versions`.`id` = 1 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1772}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.3256001, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Product.php:1772", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1772}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1772", "ajax": false, "filename": "Product.php", "line": "1772"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.707, "width_percent": 0.406}, {"sql": "select `id` from `product_version` where `product_id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 69}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.328862, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:69", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=69", "ajax": false, "filename": "VersionScoreTrait.php", "line": "69"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.114, "width_percent": 0.349}, {"sql": "select `id` from `families` where `organization_id` = 1 and (`name` = 'General' or `name` = 'SEO')", "type": "query", "params": [], "bindings": [1, "General", "SEO"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.332336, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:81", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=81", "ajax": false, "filename": "VersionScoreTrait.php", "line": "81"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.463, "width_percent": 0.368}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 88}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.335944, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:88", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=88", "ajax": false, "filename": "VersionScoreTrait.php", "line": "88"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.831, "width_percent": 0.349}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` in (4) and `value` is not null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.340152, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:93", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=93", "ajax": false, "filename": "VersionScoreTrait.php", "line": "93"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.181, "width_percent": 1.245}, {"sql": "select `id` from `product_version` where `product_id` = '4'", "type": "query", "params": [], "bindings": ["4"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 99}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.345045, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:99", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=99", "ajax": false, "filename": "VersionScoreTrait.php", "line": "99"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.426, "width_percent": 0.318}, {"sql": "select `id` from `families` where `organization_id` = 1 and (`name` = 'General' or `name` = 'SEO')", "type": "query", "params": [], "bindings": [1, "General", "SEO"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 111}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3486369, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:111", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=111", "ajax": false, "filename": "VersionScoreTrait.php", "line": "111"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.743, "width_percent": 0.368}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 118}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.3520741, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:118", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=118", "ajax": false, "filename": "VersionScoreTrait.php", "line": "118"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.111, "width_percent": 0.318}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` in (4) and `value` is null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.356445, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:124", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=124", "ajax": false, "filename": "VersionScoreTrait.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.429, "width_percent": 0.635}, {"sql": "select * from `families` where `is_default` = 0 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [0, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 188}, {"index": 16, "namespace": null, "name": "app/Models/Product/Family.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Family.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 187}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.36075, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=188", "ajax": false, "filename": "ProductController.php", "line": "188"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.064, "width_percent": 0.387}, {"sql": "select * from `organizations` where `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 191}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.364862, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:191", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=191", "ajax": false, "filename": "ProductController.php", "line": "191"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.452, "width_percent": 0.54}, {"sql": "select * from `versions` where `versions`.`id` = '1' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.368187, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:193", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=193", "ajax": false, "filename": "ProductController.php", "line": "193"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.991, "width_percent": 0.368}, {"sql": "select * from `locations` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 197}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.372288, "duration": 0.0033399999999999997, "duration_str": "3.34ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:197", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=197", "ajax": false, "filename": "ProductController.php", "line": "197"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 23.36, "width_percent": 2.121}, {"sql": "select * from `variants` where `product_id` = '4' and `version_id` = '1'", "type": "query", "params": [], "bindings": ["4", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 199}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.378503, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:199", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=199", "ajax": false, "filename": "ProductController.php", "line": "199"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 25.481, "width_percent": 0.4}, {"sql": "select * from `product_version` where `product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductScoreProgressBar.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductScoreProgressBar.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "completeness::components.product-header", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-header.blade.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.394123, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ProductScoreProgressBar.php:38", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductScoreProgressBar.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductScoreProgressBar.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FComponents%2FProductScoreProgressBar.php&line=38", "ajax": false, "filename": "ProductScoreProgressBar.php", "line": "38"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 25.881, "width_percent": 0.343}, {"sql": "select `id` from `products` where `id` > 4 and `organization_id` = '1' order by `id` asc limit 1", "type": "query", "params": [], "bindings": [4, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "components.products.edit-product-header", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4001079, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderBtns.php:35", "source": {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderBtns.php&line=35", "ajax": false, "filename": "EditProductHeaderBtns.php", "line": "35"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.224, "width_percent": 0.483}, {"sql": "select `id` from `products` where `id` < 4 and `organization_id` = '1' order by `id` desc limit 1", "type": "query", "params": [], "bindings": [4, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "components.products.edit-product-header", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.403661, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderBtns.php:36", "source": {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderBtns.php&line=36", "ajax": false, "filename": "EditProductHeaderBtns.php", "line": "36"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.707, "width_percent": 0.292}, {"sql": "select * from `families` where `is_default` = 1 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.411096, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:40", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=40", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "40"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.999, "width_percent": 0.419}, {"sql": "select * from `versions` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.414432, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:41", "source": {"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=41", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "41"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.418, "width_percent": 0.305}, {"sql": "select `id` from `product_version` where `version_id` = 1 and `product_id` = 4", "type": "query", "params": [], "bindings": [1, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.417273, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:23", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=23", "ajax": false, "filename": "VersionScoreTrait.php", "line": "23"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.723, "width_percent": 0.286}, {"sql": "select `id` from `families` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.420305, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:32", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=32", "ajax": false, "filename": "VersionScoreTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.009, "width_percent": 0.267}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.425285, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:39", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=39", "ajax": false, "filename": "VersionScoreTrait.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.276, "width_percent": 0.406}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` = 4 and `value` is not null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4289339, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:43", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=43", "ajax": false, "filename": "VersionScoreTrait.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.682, "width_percent": 0.349}, {"sql": "select count(*) as aggregate from `brand_product` where `product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.431881, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:45", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=45", "ajax": false, "filename": "VersionScoreTrait.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.031, "width_percent": 0.33}, {"sql": "select count(*) as aggregate from `category_product` where `product_id` = 4 and `category_product`.`deleted_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4348269, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:46", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=46", "ajax": false, "filename": "VersionScoreTrait.php", "line": "46"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.362, "width_percent": 0.267}, {"sql": "select count(*) as aggregate from `product_vendor` where `product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.437609, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:47", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=47", "ajax": false, "filename": "VersionScoreTrait.php", "line": "47"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.628, "width_percent": 0.311}, {"sql": "select * from `product_version` where `product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.442301, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:43", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=43", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.94, "width_percent": 0.432}, {"sql": "select * from `versions` where `versions`.`id` = 1 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 22, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.445641, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:48", "source": {"index": 20, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=48", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "48"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.372, "width_percent": 0.337}, {"sql": "select * from `products` where `products`.`id` = 4 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [4, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 20, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 21, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 22, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.448625, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.708, "width_percent": 0.286}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.451983, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.994, "width_percent": 0.305}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.4553561, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.299, "width_percent": 0.318}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at`, `invite_product`.`id` as `pivotId` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.460871, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.616, "width_percent": 0.356}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.464019, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.972, "width_percent": 0.337}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.4674358, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.309, "width_percent": 0.381}, {"sql": "select * from `variants` where `variants`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 27, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.470728, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.69, "width_percent": 0.318}, {"sql": "select `files`.*, `file_product`.`product_id` as `pivot_product_id`, `file_product`.`file_id` as `pivot_file_id`, `file_product`.`uploaded_for` as `pivot_uploaded_for`, `file_product`.`created_at` as `pivot_created_at`, `file_product`.`updated_at` as `pivot_updated_at` from `files` inner join `file_product` on `files`.`id` = `file_product`.`file_id` where `file_product`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.4788408, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.007, "width_percent": 0.502}, {"sql": "select * from `inventories` where `variant_id` is null and `inventories`.`product_id` in (4)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 27, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.482199, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.509, "width_percent": 0.33}, {"sql": "select * from `families` where exists (select * from `attribute_family_product_versions` where `families`.`id` = `attribute_family_product_versions`.`family_id` and `product_id` = 4 and `version_id` = 1) order by `id` asc", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.4852772, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.839, "width_percent": 0.4}, {"sql": "select `attributes`.*, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 21, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 23, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.489213, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.239, "width_percent": 1.27}, {"sql": "select * from `attribute_types` where `attribute_types`.`id` in (1, 3, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 27, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 28, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.494399, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.51, "width_percent": 0.413}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 27, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 28, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.49754, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.923, "width_percent": 0.305}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 1 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.5005949, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.227, "width_percent": 0.47}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 3 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 3, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.5039768, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.697, "width_percent": 0.381}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 4 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 4, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.509232, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.078, "width_percent": 0.534}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 5 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 5, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.512786, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.612, "width_percent": 0.502}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 6 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 6, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.516352, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.114, "width_percent": 0.483}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 7 and `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 7, 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.51981, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.596, "width_percent": 0.489}, {"sql": "select count(*) as aggregate from `brand_product` where `product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 401}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}], "start": **********.526255, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:401", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 401}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=401", "ajax": false, "filename": "VersionScoreTrait.php", "line": "401"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.085, "width_percent": 0.387}, {"sql": "select count(*) as aggregate from `category_product` where `product_id` = 4 and `category_product`.`deleted_at` is null", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 402}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}], "start": **********.529428, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:402", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=402", "ajax": false, "filename": "VersionScoreTrait.php", "line": "402"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.473, "width_percent": 0.337}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.534308, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.809, "width_percent": 0.47}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.537899, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.279, "width_percent": 0.457}, {"sql": "select `versions`.`id` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "view", "name": "components.products.edit-product-header-navs", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-navs.blade.php", "line": 64}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.5447981, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "components.products.edit-product-header-navs:64", "source": {"index": 17, "namespace": "view", "name": "components.products.edit-product-header-navs", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-navs.blade.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header-navs.blade.php&line=64", "ajax": false, "filename": "edit-product-header-navs.blade.php", "line": "64"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.737, "width_percent": 0.4}, {"sql": "select `id` from `families` where `is_default` = 1 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 301}, {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 25}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.55296, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:301", "source": {"index": 14, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 301}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=301", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "301"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.137, "width_percent": 0.362}, {"sql": "select `family_id` from `attribute_family_product_versions` where `attribute_family_product_versions`.`product_id` = 4 and `attribute_family_product_versions`.`product_id` is not null and `family_id` not in (1, 2)", "type": "query", "params": [], "bindings": [4, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 303}, {"index": 18, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 25}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.558366, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:303", "source": {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 303}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=303", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "303"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.499, "width_percent": 0.394}, {"sql": "(select `attribute_id`, `value`, `attributes`.`rules` from `attribute_family_product_versions` left join `attributes` on `attribute_family_product_versions`.`attribute_id` = `attributes`.`id` where `attribute_family_product_versions`.`product_id` = 4 and `attribute_family_product_versions`.`product_id` is not null and `version_id` = 1 and `attributes`.`attribute_type_id` <> 13) union (select id as attribute_id ,null as value, `rules` from `attributes` where `attribute_type_id` <> 13 and exists (select * from `families` inner join `attribute_family` on `families`.`id` = `attribute_family`.`family_id` where `attributes`.`id` = `attribute_family`.`attribute_id` and (`is_default` = 1 or 0 = 1)) and `organization_id` = '1')", "type": "query", "params": [], "bindings": [4, 1, 13, 13, 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 316}, {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.562693, "duration": 0.01964, "duration_str": "19.64ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:316", "source": {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 316}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=316", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "316"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.893, "width_percent": 12.474}, {"sql": "select * from `variants` where `product_id` = 4 and `version_id` = 1", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 163}, {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.5866961, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:163", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=163", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "163"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.366, "width_percent": 0.413}, {"sql": "select * from `settings` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 169}, {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 30}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}], "start": **********.5928779, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:63", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=63", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "63"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.779, "width_percent": 0.476}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 115}, {"index": 21, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 90}, {"index": 22, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 24, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}], "start": **********.596713, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:115", "source": {"index": 20, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=115", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "115"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.256, "width_percent": 0.413}, {"sql": "select * from `settings` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 118}, {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 90}, {"index": 18, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}], "start": **********.5998552, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:63", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=63", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "63"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.668, "width_percent": 0.394}, {"sql": "select * from `brands` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.604859, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:37", "source": {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=37", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "37"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.062, "width_percent": 2.191}, {"sql": "select * from `invites` where ((`organization_id_sender` != '1' and `email` = '<EMAIL>' and `type` = 'retailer') or (`organization_id_sender` = '1' and (`email` != '<EMAIL>' or `email` is null) and `type` = 'vendor'))", "type": "query", "params": [], "bindings": ["1", "<EMAIL>", "retailer", "1", "<EMAIL>", "vendor"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6113098, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:39", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=39", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.253, "width_percent": 0.502}, {"sql": "select * from `categories` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.615543, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:41", "source": {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=41", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "41"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.755, "width_percent": 1.264}, {"sql": "select * from `channels` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6204512, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:43", "source": {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=43", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.019, "width_percent": 1.061}, {"sql": "select `categories`.`id` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` = 4", "type": "query", "params": [], "bindings": [4], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.626182, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:45", "source": {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=45", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.08, "width_percent": 0.445}, {"sql": "select * from `categories` where `category_id` is null and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Category.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Category.php", "line": 259}, {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.6293511, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "Category.php:259", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Category.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Category.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FCategory.php&line=259", "ajax": false, "filename": "Category.php", "line": "259"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.524, "width_percent": 0.749}, {"sql": "select `value`, `key` from `settings` where `organization_id` = 1 and `key` in ('vendor', 'category', 'brand')", "type": "query", "params": [], "bindings": [1, "vendor", "category", "brand"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 51}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 16, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6337678, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:51", "source": {"index": 14, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=51", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.274, "width_percent": 0.502}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.650387, "duration": 0.0214, "duration_str": "21.4ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.775, "width_percent": 13.592}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.6845162, "duration": 0.0071200000000000005, "duration_str": "7.12ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.367, "width_percent": 4.522}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.696713, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.889, "width_percent": 0.711}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.70241, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.601, "width_percent": 0.432}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7101722, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.032, "width_percent": 0.622}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.715726, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.655, "width_percent": 1.245}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.721771, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.9, "width_percent": 0.432}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.72759, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.332, "width_percent": 0.318}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7334, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.649, "width_percent": 0.445}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.738916, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.094, "width_percent": 0.445}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.744909, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.538, "width_percent": 0.445}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.748522, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.983, "width_percent": 0.337}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.752643, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.319, "width_percent": 0.406}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.758434, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.726, "width_percent": 0.33}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.762514, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.056, "width_percent": 0.419}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7659318, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.475, "width_percent": 0.26}, {"sql": "select `id` from `organization_user` where (`user_id` = 1 and `organization_id` = '1') limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 65}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.7703202, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:57", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=57", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.736, "width_percent": 0.299}, {"sql": "select `id` from `permissions` where `handle` = 'add_and_edit_product' limit 1", "type": "query", "params": [], "bindings": ["add_and_edit_product"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.775733, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:61", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=61", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "61"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.034, "width_percent": 1.531}, {"sql": "select * from `organization_user_permissions` where (`organization_user_id` = 1 and `permission_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}], "start": **********.785468, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:71", "source": {"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=71", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "71"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.565, "width_percent": 1.296}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.794556, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.861, "width_percent": 0.464}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7985952, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.324, "width_percent": 0.286}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8032742, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.61, "width_percent": 0.394}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8083491, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.004, "width_percent": 0.299}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.812355, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.302, "width_percent": 0.394}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8157778, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.696, "width_percent": 0.273}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.820321, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.969, "width_percent": 0.438}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.824948, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 93.407, "width_percent": 0.311}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.828969, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 93.719, "width_percent": 0.381}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8324308, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.1, "width_percent": 0.476}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8368921, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.576, "width_percent": 0.394}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8416622, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.97, "width_percent": 0.286}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.845715, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.256, "width_percent": 0.584}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8499372, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.84, "width_percent": 0.292}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.854187, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.132, "width_percent": 0.464}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.859428, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.596, "width_percent": 0.318}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.8637471, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.913, "width_percent": 0.413}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.867355, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.326, "width_percent": 0.286}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.871402, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.612, "width_percent": 0.4}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.876936, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.012, "width_percent": 0.305}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.881861, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "layouts.navs.product_sidebar:435", "source": {"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=435", "ajax": false, "filename": "product_sidebar.blade.php", "line": "435"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.317, "width_percent": 0.483}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, "1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.887344, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:249", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=249", "ajax": false, "filename": "app_new.blade.php", "line": "249"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.8, "width_percent": 0.483}, {"sql": "select * from `versions` where `versions`.`organization_id` = 1 and `versions`.`organization_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.892011, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:251", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=251", "ajax": false, "filename": "app_new.blade.php", "line": "251"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.282, "width_percent": 0.368}, {"sql": "select count(*) as aggregate from `products` where `organization_id` = '1' and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, {"index": 17, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 360}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.897579, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "User.php:193", "source": {"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=193", "ajax": false, "filename": "User.php", "line": "193"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.651, "width_percent": 0.349}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Product\\AttributeFamilyProductVersion": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=1", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "?"}}, "App\\Models\\Setting": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}, "App\\Models\\Product\\ProductVersion": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProductVersion.php&line=1", "ajax": false, "filename": "ProductVersion.php", "line": "?"}}, "App\\Models\\Product\\AttributeType": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeType.php&line=1", "ajax": false, "filename": "AttributeType.php", "line": "?"}}, "App\\Models\\Product\\Variant": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVariant.php&line=1", "ajax": false, "filename": "Variant.php", "line": "?"}}, "App\\Models\\Product\\Product": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Channel\\Channel": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Location\\Location": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FLocation%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUserPermission": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUserPermission.php&line=1", "ajax": false, "filename": "OrganizationUserPermission.php", "line": "?"}}}, "count": 130, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 21, "messages": [{"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1223396379 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1223396379\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172088, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => inventory,\n  result => true,\n  user => 1,\n  arguments => [0 => inventory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1827830258 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"9 characters\">inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[0 =&gt; inventory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1827830258\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.543826, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-550969500 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-550969500\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695222, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1953168111 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953168111\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.708437, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-635593871 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-635593871\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.720563, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => invite-team,\n  result => true,\n  user => 1,\n  arguments => [0 => invite-team]\n]", "message_html": "<pre class=sf-dump id=sf-dump-197413467 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess invite-team</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"11 characters\">invite-team</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"18 characters\">[0 =&gt; invite-team]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-197413467\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.731905, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => notification,\n  result => true,\n  user => 1,\n  arguments => [0 => notification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1669633318 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess notification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"12 characters\">notification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"19 characters\">[0 =&gt; notification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669633318\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.743832, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => settings,\n  result => true,\n  user => 1,\n  arguments => [0 => settings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-411697373 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; settings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-411697373\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.751741, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-265081888 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-265081888\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.761665, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => import,\n  result => true,\n  user => 1,\n  arguments => [0 => import]\n]", "message_html": "<pre class=sf-dump id=sf-dump-624134997 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess import</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; import]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624134997\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.768952, "xdebug_link": null}, {"message": "[\n  ability => add_and_edit_product,\n  target => App\\Models\\Organization\\OrganizationUserPermission,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Organization\\OrganizationUserPermission, 1 => 1]\n]", "message_html": "<pre class=sf-dump id=sf-dump-962914890 data-indent-pad=\"  \"><span class=sf-dump-note>add_and_edit_product App\\Models\\Organization\\OrganizationUserPermission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">add_and_edit_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"50 characters\">App\\Models\\Organization\\OrganizationUserPermission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"65 characters\">[0 =&gt; App\\Models\\Organization\\OrganizationUserPermission, 1 =&gt; 1]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-962914890\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.793449, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => export,\n  result => true,\n  user => 1,\n  arguments => [0 => export]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2117886245 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess export</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; export]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2117886245\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.802387, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => category,\n  result => true,\n  user => 1,\n  arguments => [0 => category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1337286675 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1337286675\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.811485, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute-set,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute-set]\n]", "message_html": "<pre class=sf-dump id=sf-dump-745954476 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute-set</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"13 characters\">attribute-set</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[0 =&gt; attribute-set]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-745954476\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.819408, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1490568201 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"9 characters\">attribute</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[0 =&gt; attribute]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1490568201\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.828107, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => variant-option,\n  result => true,\n  user => 1,\n  arguments => [0 => variant-option]\n]", "message_html": "<pre class=sf-dump id=sf-dump-301897226 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess variant-option</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"14 characters\">variant-option</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"21 characters\">[0 =&gt; variant-option]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-301897226\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.835983, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => brand,\n  result => true,\n  user => 1,\n  arguments => [0 => brand]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1634618302 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess brand</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">brand</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; brand]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1634618302\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.844737, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => vendor,\n  result => true,\n  user => 1,\n  arguments => [0 => vendor]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1923958185 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess vendor</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">vendor</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; vendor]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1923958185\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.853041, "xdebug_link": null}, {"message": "[\n  ability => create-lang,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => [0 => Object(Illuminate\\Database\\Eloquent\\Builder)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-31228645 data-indent-pad=\"  \"><span class=sf-dump-note>create-lang </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-lang</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"51 characters\">[0 =&gt; Object(Illuminate\\Database\\Eloquent\\Builder)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-31228645\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.862627, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => location,\n  result => true,\n  user => 1,\n  arguments => [0 => location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1612791643 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1612791643\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.870489, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => store,\n  result => true,\n  user => 1,\n  arguments => [0 => store]\n]", "message_html": "<pre class=sf-dump id=sf-dump-212168443 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess store</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; store]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-212168443\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.880368, "xdebug_link": null}]}, "session": {"_token": "9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/4/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "organization_id": "1", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:49 [\n          \"Handle\" => \"Handle\"\n          \"Title\" => \"Title\"\n          \"Body (HTML)\" => \"Body (HTML)\"\n          \"Vendor\" => \"Vendor\"\n          \"Type\" => \"Type\"\n          \"Tags\" => \"Tags\"\n          \"Published\" => \"Published\"\n          \"Option1 Name\" => \"Option1 Name\"\n          \"Option1 Value\" => \"Option1 Value\"\n          \"Option2 Name\" => \"Option2 Name\"\n          \"Option2 Value\" => \"Option2 Value\"\n          \"Option3 Name\" => \"Option3 Name\"\n          \"Option3 Value\" => \"Option3 Value\"\n          \"Variant SKU\" => \"Variant SKU\"\n          \"Variant Grams\" => \"Variant Grams\"\n          \"Variant Inventory Tracker\" => \"Variant Inventory Tracker\"\n          \"Variant Inventory Qty\" => \"Variant Inventory Qty\"\n          \"Variant Inventory Policy\" => \"Variant Inventory Policy\"\n          \"Variant Fulfillment Service\" => \"Variant Fulfillment Service\"\n          \"Variant Price\" => \"Variant Price\"\n          \"Variant Compare At Price\" => \"Variant Compare At Price\"\n          \"Variant Requires Shipping\" => \"Variant Requires Shipping\"\n          \"Variant Taxable\" => \"Variant Taxable\"\n          \"Variant Barcode\" => \"Variant Barcode\"\n          \"Image Src\" => \"Image Src\"\n          \"Image Position\" => \"Image Position\"\n          \"Image Alt Text\" => \"Image Alt Text\"\n          \"Gift Card\" => \"Gift Card\"\n          \"SEO Title\" => \"SEO Title\"\n          \"SEO Description\" => \"SEO Description\"\n          \"Google Shopping / Google Product Category\" => \"Google Shopping / Google Product Category\"\n          \"Google Shopping / Gender\" => \"Google Shopping / Gender\"\n          \"Google Shopping / Age Group\" => \"Google Shopping / Age Group\"\n          \"Google Shopping / MPN\" => \"Google Shopping / MPN\"\n          \"Google Shopping / AdWords Grouping\" => \"Google Shopping / AdWords Grouping\"\n          \"Google Shopping / AdWords Labels\" => \"Google Shopping / AdWords Labels\"\n          \"Google Shopping / Condition\" => \"Google Shopping / Condition\"\n          \"Google Shopping / Custom Product\" => \"Google Shopping / Custom Product\"\n          \"Google Shopping / Custom Label 0\" => \"Google Shopping / Custom Label 0\"\n          \"Google Shopping / Custom Label 1\" => \"Google Shopping / Custom Label 1\"\n          \"Google Shopping / Custom Label 2\" => \"Google Shopping / Custom Label 2\"\n          \"Google Shopping / Custom Label 3\" => \"Google Shopping / Custom Label 3\"\n          \"Google Shopping / Custom Label 4\" => \"Google Shopping / Custom Label 4\"\n          \"Variant Image\" => \"Variant Image\"\n          \"Variant Weight Unit\" => \"Variant Weight Unit\"\n          \"Variant Tax Code\" => \"Variant Tax Code\"\n          \"Cost per item\" => \"Cost per item\"\n          \"Status\" => \"Status\"\n          \"List\" => \"List\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:49 [\n        0 => array:2 [\n          \"label\" => \"Handle\"\n          \"value\" => \"Default,Handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Title\"\n          \"value\" => \"Default,Title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Body (HTML)\"\n          \"value\" => \"Default,Body (HTML)\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,Vendor\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Type\"\n          \"value\" => \"Default,Type\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"Default,Tags\"\n        ]\n        6 => array:2 [\n          \"label\" => \"Published\"\n          \"value\" => \"Default,Published\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Option1 Name\"\n          \"value\" => \"Default,Option1 Name\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Option1 Value\"\n          \"value\" => \"Default,Option1 Value\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Option2 Name\"\n          \"value\" => \"Default,Option2 Name\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Option2 Value\"\n          \"value\" => \"Default,Option2 Value\"\n        ]\n        11 => array:2 [\n          \"label\" => \"Option3 Name\"\n          \"value\" => \"Default,Option3 Name\"\n        ]\n        12 => array:2 [\n          \"label\" => \"Option3 Value\"\n          \"value\" => \"Default,Option3 Value\"\n        ]\n        13 => array:2 [\n          \"label\" => \"Variant SKU\"\n          \"value\" => \"Default,Variant SKU\"\n        ]\n        14 => array:2 [\n          \"label\" => \"Variant Grams\"\n          \"value\" => \"Default,Variant Grams\"\n        ]\n        15 => array:2 [\n          \"label\" => \"Variant Inventory Tracker\"\n          \"value\" => \"Default,Variant Inventory Tracker\"\n        ]\n        16 => array:2 [\n          \"label\" => \"Variant Inventory Qty\"\n          \"value\" => \"Default,Variant Inventory Qty\"\n        ]\n        17 => array:2 [\n          \"label\" => \"Variant Inventory Policy\"\n          \"value\" => \"Default,Variant Inventory Policy\"\n        ]\n        18 => array:2 [\n          \"label\" => \"Variant Fulfillment Service\"\n          \"value\" => \"Default,Variant Fulfillment Service\"\n        ]\n        19 => array:2 [\n          \"label\" => \"Variant Price\"\n          \"value\" => \"Default,Variant Price\"\n        ]\n        20 => array:2 [\n          \"label\" => \"Variant Compare At Price\"\n          \"value\" => \"Default,Variant Compare At Price\"\n        ]\n        21 => array:2 [\n          \"label\" => \"Variant Requires Shipping\"\n          \"value\" => \"Default,Variant Requires Shipping\"\n        ]\n        22 => array:2 [\n          \"label\" => \"Variant Taxable\"\n          \"value\" => \"Default,Variant Taxable\"\n        ]\n        23 => array:2 [\n          \"label\" => \"Variant Barcode\"\n          \"value\" => \"Default,Variant Barcode\"\n        ]\n        24 => array:2 [\n          \"label\" => \"Image Src\"\n          \"value\" => \"Default,Image Src\"\n        ]\n        25 => array:2 [\n          \"label\" => \"Image Position\"\n          \"value\" => \"Default,Image Position\"\n        ]\n        26 => array:2 [\n          \"label\" => \"Image Alt Text\"\n          \"value\" => \"Default,Image Alt Text\"\n        ]\n        27 => array:2 [\n          \"label\" => \"Gift Card\"\n          \"value\" => \"Default,Gift Card\"\n        ]\n        28 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"Default,SEO Title\"\n        ]\n        29 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"Default,SEO Description\"\n        ]\n        30 => array:2 [\n          \"label\" => \"Google Shopping / Google Product Category\"\n          \"value\" => \"Default,Google Shopping / Google Product Category\"\n        ]\n        31 => array:2 [\n          \"label\" => \"Google Shopping / Gender\"\n          \"value\" => \"Default,Google Shopping / Gender\"\n        ]\n        32 => array:2 [\n          \"label\" => \"Google Shopping / Age Group\"\n          \"value\" => \"Default,Google Shopping / Age Group\"\n        ]\n        33 => array:2 [\n          \"label\" => \"Google Shopping / MPN\"\n          \"value\" => \"Default,Google Shopping / MPN\"\n        ]\n        34 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Grouping\"\n          \"value\" => \"Default,Google Shopping / AdWords Grouping\"\n        ]\n        35 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Labels\"\n          \"value\" => \"Default,Google Shopping / AdWords Labels\"\n        ]\n        36 => array:2 [\n          \"label\" => \"Google Shopping / Condition\"\n          \"value\" => \"Default,Google Shopping / Condition\"\n        ]\n        37 => array:2 [\n          \"label\" => \"Google Shopping / Custom Product\"\n          \"value\" => \"Default,Google Shopping / Custom Product\"\n        ]\n        38 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 0\"\n          \"value\" => \"Default,Google Shopping / Custom Label 0\"\n        ]\n        39 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 1\"\n          \"value\" => \"Default,Google Shopping / Custom Label 1\"\n        ]\n        40 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 2\"\n          \"value\" => \"Default,Google Shopping / Custom Label 2\"\n        ]\n        41 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 3\"\n          \"value\" => \"Default,Google Shopping / Custom Label 3\"\n        ]\n        42 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 4\"\n          \"value\" => \"Default,Google Shopping / Custom Label 4\"\n        ]\n        43 => array:2 [\n          \"label\" => \"Variant Image\"\n          \"value\" => \"Default,Variant Image\"\n        ]\n        44 => array:2 [\n          \"label\" => \"Variant Weight Unit\"\n          \"value\" => \"Default,Variant Weight Unit\"\n        ]\n        45 => array:2 [\n          \"label\" => \"Variant Tax Code\"\n          \"value\" => \"Default,Variant Tax Code\"\n        ]\n        46 => array:2 [\n          \"label\" => \"Cost per item\"\n          \"value\" => \"Default,Cost per item\"\n        ]\n        47 => array:2 [\n          \"label\" => \"Status\"\n          \"value\" => \"Default,Status\"\n        ]\n        48 => array:2 [\n          \"label\" => \"List\"\n          \"value\" => \"Default,List\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"data_required\" => array:8 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"1\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n  ]\n  \"import_action\" => \"3\"\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,1\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:49 [\n    0 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n    ]\n    1 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    2 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    3 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n    ]\n    4 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    5 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n    ]\n    6 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    7 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    8 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    9 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    10 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    11 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    12 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    13 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    14 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    15 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    16 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    17 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    18 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    19 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    20 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    21 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    22 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    23 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    24 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    25 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    26 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    27 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    28 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n    ]\n    29 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n    ]\n    30 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    31 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    32 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    33 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    34 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    35 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    36 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    37 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    38 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    39 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    40 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 2\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    41 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 3\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    42 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 4\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    43 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Image\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    44 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Weight Unit\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    45 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Tax Code\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    46 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Cost per item\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    47 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Status\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    48 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,List\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n  ]\n]", "bulk_edit_data": "array:3 [\n  \"target_page\" => 1\n  \"version_id\" => \"1\"\n  \"productIds\" => array:1 [\n    0 => \"1\"\n  ]\n]", "PHPDEBUGBAR_STACK_DATA": "array:2 [\n  \"01JZ7G0H9ZJ88GPK4BAFQF308K\" => null\n  \"01JZ7G0JEKZF465Z9106YTGVG8\" => null\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/products/4/edit/1", "action_name": "products.edit", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@edit", "uri": "GET products/{id}/edit/{version_id?}", "controller": "App\\Http\\Controllers\\Product\\ProductController@edit<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:125-217</a>", "middleware": "web, check_session", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4cdc1f-b7b9-41d1-98fd-bbaca9f8e490\" target=\"_blank\">View in Telescope</a>", "duration": "1.45s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-151727662 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-151727662\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1056131154 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1056131154\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1709184748 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/products?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjhMVnlqdjBjUmVBQXVxbUU3Y3d3MlE9PSIsInZhbHVlIjoiZ2ZJOVJXbGVMN2ZuQUpDZmJ3RjFWVTRMMlZlZFZLdzdRVi9aMHRaaGlCTlVybmp1RVpDQ0xkRDUwT2tvZkFUTXlsSFRndHhOS2RDQUZrcTc5TTZCSUtQTm1tZmVBdk5DekRWNGx4TkVsSGlhajZuZFhHRVEwN3dGRW1DdTVGWHciLCJtYWMiOiI2M2ZmMWI1YmRhZjhiMDcxNGZlMmQ2YTYyODRhZmY2YTI2NGYxZGYxY2NhOWJlYTIzNTI5ZGU4NjY5NDQ4MWQ5IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImVrcGFvM3pUVEZ4bFlYSjM3VVpxU3c9PSIsInZhbHVlIjoicjltdVh2YTNYUVM5a005RXVwNkVTNitYK3Yrb3FBRDYwRmdIUlhheXlLOENPYXN3WHZrNDlnTzcxOWE3YkY3VDhkQmJzTHV5WkEyMjNub1Zpak9LTThleWNuVjlOQ1h3M2FQYjhzanZSZ0JMOURaOGtnM0pwOUFNVVB2TS95ZEYiLCJtYWMiOiJmMDRkZmFlYmZkZjllZTI3ODQ0YzhkZTU3OWM0MWFlZWNkNDhmZWEwN2VhZjNhOTZjOWJiYTdiYzU2ZDdhZDVmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1709184748\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2041888439 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hpj02iYobWSFRbp0Zjz10UyQzMp42LbVgb86a16Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2041888439\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1049140950 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:36:33 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049140950\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-398688307 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/products/4/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Handle</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>Body (HTML)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>Vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>Published</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>Option1 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>Option1 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>Option2 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>Option2 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>Option3 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>Option3 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>Variant SKU</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>Variant Grams</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Tracker</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Qty</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>Variant Fulfillment Service</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>Variant Price</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>Variant Compare At Price</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>Variant Requires Shipping</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>Variant Taxable</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>Variant Barcode</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>Image Src</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>Image Position</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>Image Alt Text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>Gift Card</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>SEO Title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>SEO Description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Google Product Category</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Gender</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Age Group</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>Google Shopping / MPN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Grouping</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Labels</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Condition</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Product</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 0</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 1</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 2</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 3</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 4</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>Variant Image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>Variant Weight Unit</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>Variant Tax Code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>Cost per item</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n          </samp>]\n          <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n          </samp>]\n          <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n          </samp>]\n          <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n          </samp>]\n          <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n          </samp>]\n          <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n          </samp>]\n          <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n          </samp>]\n          <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n          </samp>]\n          <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n          </samp>]\n          <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n          </samp>]\n          <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n          </samp>]\n          <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n          </samp>]\n          <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n          </samp>]\n          <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n          </samp>]\n          <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n          </samp>]\n          <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n          </samp>]\n          <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n          </samp>]\n          <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n          </samp>]\n          <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n          </samp>]\n          <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n          </samp>]\n          <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n          </samp>]\n          <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n          </samp>]\n          <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n          </samp>]\n          <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n          </samp>]\n          <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>bulk_edit_data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>target_page</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>productIds</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ7G0H9ZJ88GPK4BAFQF308K</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01JZ7G0JEKZF465Z9106YTGVG8</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-398688307\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/products/4/edit/1", "action_name": "products.edit", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@edit"}, "badge": null}}