<?php ?>

<?php if(Request::segment(4) == 'edit'): ?>
    <?php $__env->startSection('titles','Edit Category'); ?>
<?php else: ?>
    <?php $__env->startSection('titles','Add Categories'); ?>
<?php endif; ?>
<?php $__env->startSection('content'); ?>
    <?php $__env->startPush("header_scripts"); ?>
        <link href="https://www.jqueryscript.net/css/jquerysctipttop.css" rel="stylesheet" type="text/css">
        <link rel="stylesheet" href="https://cdn.materialdesignicons.com/5.0.45/css/materialdesignicons.min.css">
        <link rel="stylesheet" href="<?php echo e(asset('treeselect/style.css')); ?>">

            <link rel="stylesheet" href="<?php echo e(asset('treeSelectJs/treeselectjs.css')); ?>" />

    <?php $__env->stopPush(); ?>
    <div>
        <?php if (isset($component)) { $__componentOriginal7a044565d18b640bc08243f7f8ff3e54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a044565d18b640bc08243f7f8ff3e54 = $attributes; } ?>
<?php $component = App\View\Components\Products\AddPageTitle::resolve(['name' => ''.e(trans('products_categories.page_title')).'','routes' => route('categories.index')] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.add-page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\AddPageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'false']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a044565d18b640bc08243f7f8ff3e54)): ?>
<?php $attributes = $__attributesOriginal7a044565d18b640bc08243f7f8ff3e54; ?>
<?php unset($__attributesOriginal7a044565d18b640bc08243f7f8ff3e54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a044565d18b640bc08243f7f8ff3e54)): ?>
<?php $component = $__componentOriginal7a044565d18b640bc08243f7f8ff3e54; ?>
<?php unset($__componentOriginal7a044565d18b640bc08243f7f8ff3e54); ?>
<?php endif; ?>
        <div class="row mt-4">
            <div class="col-12 col-md-9 col-lg-9 col-xl-5">
                <form id="pro_cat_create_form" class="formStyle"
                      action="<?php echo e(isset($category) ? route('categories.update',$category->id) : route('categories.store')); ?>"
                      method="post">
                    <?php echo csrf_field(); ?>
                    <?php if(isset($category)): ?>
                        <?php echo method_field('PUT'); ?>
                        <input type="hidden" name="id" value="<?php echo e($category->id); ?>">
                    <?php endif; ?>
                    <div class="form-group">
                        <label for="name"><?php echo e(trans('products_categories_create.category_name')); ?>

                            &nbsp;<span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="name" name="name" value="<?php echo e(isset($category) ? $category->name : old('name')); ?>" autofocus required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger">
                        <small><?php echo e($message); ?></small>
                        </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="form-group mt-3 mb-0">
                        <label for="description"><?php echo e(trans('products_categories_create.description')); ?></label>
                        <textarea type="text" class="form-control h-120 <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="description" name="description"><?php echo e(isset($category)? $category->description : old('description')); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger">
                        <small><?php echo e($message); ?></small>
                        </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>

                    <label class="mt-3"
                           for="status1"><?php echo e(trans('products_categories_create.category_toggle')); ?></label>
                    <div class="custom-control custom-switch mt-2 d-flex align-items-center">
                        <label for="status1" class="me-2 Roboto regular">
                            No
                        </label>
                        <div class="form-check form-switch d-inline" id="draft">
                            <input name="status" type="checkbox" value="1" class="form-check-input Assign-parent-category" data-confirm-before-leave="true"  id="status1" <?php echo e(isset($category) ? (isset($category->category_id)? 'checked' : '') : 'checked'); ?>>
                            <label class="ms-2" for="status1">
                                <?php echo e(trans('products_attributes_create.yes')); ?>

                            </label>
                        </div>
                    </div>
                    <div class="form-group mt-3 mb-0 parent-category-div">
                        <label for="description"><?php echo e(trans('products_categories_create.parent_category')); ?></label>
                        <div class="categoryList" >
                        </div>
                        <input id="category_id" name="category_id" type="hidden" />
                           
                    </div>
                    <div class="form-group mt-40 mb-4">
                        <div class="d-flex justify-content-end">
                            <a href="<?php echo e(route('categories.index')); ?>"
                                   class="btn btn-outline-danger" id="cancel-btn">
                                    <?php echo e(trans('products_categories_create.cancel_btn')); ?>

                            </a>
                            <button type="submit" id="pro_cat_create_btn"
                                    class="btn btn-primary ms-2">
                                <?php echo e(trans('products_categories_create.save_btn')); ?>

                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php $__env->startPush('footer_scripts'); ?>
        <script src="<?php echo e(asset('treeselect/comboTreePlugin.js')); ?>" type="text/javascript"></script>
        <script src="<?php echo e(asset('treeSelectJs/treeselectjs.umd.js')); ?>" ></script>
        <script type="text/javascript">

            const options = <?php echo json_encode($category_data, 15, 512) ?>

            const treeselect = new Treeselect({
                parentHtmlContainer: document.querySelector('.categoryList'),
                options: options,
                //isGroupedValue: true,
                clearable: false,
                isIndependentNodes: true,
                isSingleSelect: true,
                value: [<?php echo e(isset($category)?$category->category_id:""); ?>],
                searchable: false,
            })

            

            treeselect.srcElement.addEventListener('input', (e) => {
                $("#category_id").val(e.detail);
            })

            if (treeselect.value) {
                $("#category_id").val(treeselect.value);
            }


            var SampleJSONData = <?php echo json_encode($category_data, 15, 512) ?>;
            var comboTree2;
            $(document).ready(function ($) {
                comboTree2 = $('#justAnotherInputBox').comboTree({
                    source: SampleJSONData,
                    isMultiple: false,
                    selected: ['<?php echo e(isset($category)?$category->category_id:""); ?>']
                });
                $("#justAnotherInputBox").change(function () {
                    let id = comboTree2.getSelectedIds();
                    if( $("#justAnotherInputBox").val()){
                        $('#category_id').val(id);
                    }else {
                        $('#category_id').val(null);
                    }
                });

            });

            $(document).ready(function () {
                <?php if(isset($category) && isset($category->category_id)): ?>
                $('.parent-category-div').css('display','block')
                <?php elseif(isset($category) && !isset($category->category_id)): ?>
                $('.parent-category-div').css('display','none')
                <?php else: ?>
                $('.parent-category-div').css('display','block')
                <?php endif; ?>
            })

            $('.Assign-parent-category').on('change',function () {
                if (!($(this).is(":checked"))) {
                    $('#category_id').val(null);
                    $('.parent-category-div').css('display','none')
                }else{
                    $('.parent-category-div').css('display','block')
                }
            })

        </script>
    <?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/categories/add.blade.php ENDPATH**/ ?>