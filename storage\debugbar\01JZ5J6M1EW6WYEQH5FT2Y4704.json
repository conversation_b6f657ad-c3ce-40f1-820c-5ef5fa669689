{"__meta": {"id": "01JZ5J6M1EW6WYEQH5FT2Y4704", "datetime": "2025-07-02 12:36:18", "utime": **********.607901, "method": "GET", "uri": "/api/2024-12/product-quality-score", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751459777.845193, "end": **********.607931, "duration": 0.7627379894256592, "duration_str": "763ms", "measures": [{"label": "Booting", "start": 1751459777.845193, "relative_start": 0, "end": **********.491976, "relative_end": **********.491976, "duration": 0.****************, "duration_str": "647ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.492, "relative_start": 0.****************, "end": **********.607934, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.516133, "relative_start": 0.****************, "end": **********.524297, "relative_end": **********.524297, "duration": 0.008163928985595703, "duration_str": "8.16ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.599743, "relative_start": 0.****************, "end": **********.600489, "relative_end": **********.600489, "duration": 0.0007460117340087891, "duration_str": "746μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.603636, "relative_start": 0.****************, "end": **********.603799, "relative_end": **********.603799, "duration": 0.00016307830810546875, "duration_str": "163μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/product-quality-score", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=131\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=131\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:131-158</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00513, "accumulated_duration_str": "5.13ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.5584931, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 74.464}, {"sql": "select\nSUM(CASE WHEN mean_score >= 90 THEN 1 ELSE 0 END) AS good,\nSUM(CASE WHEN mean_score >= 50 AND mean_score < 90 THEN 1 ELSE 0 END) AS fair,\nSUM(CASE WHEN mean_score < 50 THEN 1 ELSE 0 END) AS bad\nfrom (select products.id, AVG(product_version.score) as mean_score from `products` inner join `product_version` on `products`.`id` = `product_version`.`product_id` where `organization_id` is null group by `products`.`id`) as avg_scores limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 148}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.587539, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:148", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=148", "ajax": false, "filename": "DashboardController.php", "line": "148"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.464, "width_percent": 25.536}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "U6UUj3nkXuweqxQgfKo1BsNnjzf8iwgBRJ79pSv1", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/product-quality-score\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/product-quality-score", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore", "uri": "GET api/2024-12/product-quality-score", "controller": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=131\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=131\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:131-158</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4b59cb-5c8c-48da-bb98-1e3c7d847542\" target=\"_blank\">View in Telescope</a>", "duration": "767ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-684421790 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-684421790\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2130735936 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2130735936\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-870938229 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkZvWmJmZGVBZTNVYXdtSmlNMVpEU2c9PSIsInZhbHVlIjoieFFDMTJxaEpEOW9JbkNhV3lFVFZFMXlaaXA3NXM0WURRWU9pR056YVpwOTRPcHRHSlpudmdjMGtCVE5nNmJMVzNWWnJ1bE9pM2xQeFBKclc3SDlsa1pTajIyR1FldlJvWjZtVmpHYXpjVWhhN0tJbmVjZzFNcnNYSS9kWVNEVnoiLCJtYWMiOiIwOWIyMThiMzIyNDEzMzg3NzYxZGMxOTVlMTc2YjUzOTdlMjdlYjNiZGM3NDUzN2Y2ODg0ZjVlNzQ5ODcwMTI3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkZvWmJmZGVBZTNVYXdtSmlNMVpEU2c9PSIsInZhbHVlIjoieFFDMTJxaEpEOW9JbkNhV3lFVFZFMXlaaXA3NXM0WURRWU9pR056YVpwOTRPcHRHSlpudmdjMGtCVE5nNmJMVzNWWnJ1bE9pM2xQeFBKclc3SDlsa1pTajIyR1FldlJvWjZtVmpHYXpjVWhhN0tJbmVjZzFNcnNYSS9kWVNEVnoiLCJtYWMiOiIwOWIyMThiMzIyNDEzMzg3NzYxZGMxOTVlMTc2YjUzOTdlMjdlYjNiZGM3NDUzN2Y2ODg0ZjVlNzQ5ODcwMTI3IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkM4cUNDOS9WNDdLeUlYUGVwaTBDaFE9PSIsInZhbHVlIjoiR0pyblh6UG1VeG5BSW84Q2hIL3I2QjI1cksyd1NpaUg1ci9iUWZTS093b3kzZit0Y1dpRTVLR2xTQjNld1VFK0tkT0xnOGc2Syt2VnpvNzJ3QmZ2NlRNSEN6Q2JleU8weGZ2Znc1OE5pKzZhaElXTU1UTDFzV3JKbWpxaG9PeU4iLCJtYWMiOiI2OGZmY2RjMzk3MDYzZWQ3MmM0OTY3ZjY3OGVlZWY0OTM5M2M3YjNiNzc0ZDZlMjgzNmEyZDVkNzYwYTYyZmU4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-870938229\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-43047939 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U6UUj3nkXuweqxQgfKo1BsNnjzf8iwgBRJ79pSv1</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">OxeKVJQisWQNIFYuisdXSEc5NtuRIm0BXlzs1YjJ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-43047939\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-323464751 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 12:36:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">56</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InFYZzd0ZjJSQ1RPVWwwRjh3aS91Y3c9PSIsInZhbHVlIjoiRUxsOWU1MEdvNGlqcVc4TzV1b0wxa1g5UlBvSko5alZQa082cC9EMFlOS1FYM3M1WlFpeTJTSldMaWd5eTZQYlBMRktFSjFTNDdxOVZQbTlHd2R3Q0RKemQxcUxBYXo4TDRIcVlqVVJYVlRCSlJFRGV2M2c1ZnhSSWd2ZWRXYjEiLCJtYWMiOiJlMmI5NDA1MWIxYTc1NTQxMjY5MGI5ODJiN2JhNjQ5ZjAxOWE1N2MxY2M2ODhjNWJkYjUyZThmYmI5NTMxYTA4IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 14:36:18 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Im9uUGVWbjF1c2cxVzQ4K2EvQzdxZUE9PSIsInZhbHVlIjoiWVpzbTVmWkU2Vmw3ZDQ0L0g0OWlqRDU3OG01cm4xYTN4THE0MkxQcHJXWm95Q0lLbVA4VXhzQXVOeUlrY1U0Z3hTelBjdFRXN2VmTktzZmRSRmJtT0JKMmlmei9nVnJsVlFrTEV2Ti8yS3dVbTkxbDVINXRkOW5pdzlJbTRpdDgiLCJtYWMiOiI1YjliOGY5NjU4ZTg2YTFhNGUxZDYxZDYyMzFkOTgxMmIzODI1MzBhYTU0NDg0YTJhM2FmMGVmNzVmYTk2MzMyIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 14:36:18 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InFYZzd0ZjJSQ1RPVWwwRjh3aS91Y3c9PSIsInZhbHVlIjoiRUxsOWU1MEdvNGlqcVc4TzV1b0wxa1g5UlBvSko5alZQa082cC9EMFlOS1FYM3M1WlFpeTJTSldMaWd5eTZQYlBMRktFSjFTNDdxOVZQbTlHd2R3Q0RKemQxcUxBYXo4TDRIcVlqVVJYVlRCSlJFRGV2M2c1ZnhSSWd2ZWRXYjEiLCJtYWMiOiJlMmI5NDA1MWIxYTc1NTQxMjY5MGI5ODJiN2JhNjQ5ZjAxOWE1N2MxY2M2ODhjNWJkYjUyZThmYmI5NTMxYTA4IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 14:36:18 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Im9uUGVWbjF1c2cxVzQ4K2EvQzdxZUE9PSIsInZhbHVlIjoiWVpzbTVmWkU2Vmw3ZDQ0L0g0OWlqRDU3OG01cm4xYTN4THE0MkxQcHJXWm95Q0lLbVA4VXhzQXVOeUlrY1U0Z3hTelBjdFRXN2VmTktzZmRSRmJtT0JKMmlmei9nVnJsVlFrTEV2Ti8yS3dVbTkxbDVINXRkOW5pdzlJbTRpdDgiLCJtYWMiOiI1YjliOGY5NjU4ZTg2YTFhNGUxZDYxZDYyMzFkOTgxMmIzODI1MzBhYTU0NDg0YTJhM2FmMGVmNzVmYTk2MzMyIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 14:36:18 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-323464751\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">U6UUj3nkXuweqxQgfKo1BsNnjzf8iwgBRJ79pSv1</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/api/2024-12/product-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/product-quality-score", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@ProductQualityScore"}, "badge": null}}