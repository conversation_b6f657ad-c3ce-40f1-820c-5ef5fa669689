{"__meta": {"id": "01JZ520HGE5F85CQ9H6V13RWK5", "datetime": "2025-07-02 07:53:22", "utime": **********.192356, "method": "POST", "uri": "/products/fetch/bulk/edit", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:53:22] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.0198, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.369794, "end": **********.192381, "duration": 0.8225870132446289, "duration_str": "823ms", "measures": [{"label": "Booting", "start": **********.369794, "relative_start": 0, "end": **********.903471, "relative_end": **********.903471, "duration": 0.****************, "duration_str": "534ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.903492, "relative_start": 0.****************, "end": **********.192383, "relative_end": 2.1457672119140625e-06, "duration": 0.***************, "duration_str": "289ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.918534, "relative_start": 0.****************, "end": **********.923581, "relative_end": **********.923581, "duration": 0.005046844482421875, "duration_str": "5.05ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.171387, "relative_start": 0.****************, "end": **********.188366, "relative_end": **********.188366, "duration": 0.016978979110717773, "duration_str": "16.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 1, "nb_templates": 1, "templates": [{"name": "1x BulkEdit", "param_count": null, "params": [], "start": **********.184309, "type": "react", "hash": "reactC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\js/Pages/BulkEdit.jsxBulkEdit", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fjs%2FPages%2FBulkEdit.jsx&line=1", "ajax": false, "filename": "BulkEdit.jsx", "line": "?"}, "render_count": 1, "name_original": "BulkEdit"}]}, "route": {"uri": "POST products/fetch/bulk/edit", "middleware": "web, check_billing", "controller": "App\\Http\\Controllers\\Product\\BulkEditController@fetch_apimio_products<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FBulkEditController.php&line=222\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "as": "fetch.bulk.edit.products", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FBulkEditController.php&line=222\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/BulkEditController.php:222-252</a>"}, "queries": {"count": 36, "nb_statements": 36, "nb_visible_statements": 36, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05928000000000002, "accumulated_duration_str": "59.28ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 40}, {"index": 24, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}], "start": **********.947785, "duration": 0.02677, "duration_str": "26.77ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 45.159}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.984493, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.159, "width_percent": 4.943}, {"sql": "select * from `organizations` where `organizations`.`id` = 1 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 87}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9989111, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "check_billing:21", "source": {"index": 21, "namespace": "middleware", "name": "check_billing", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\BillingMiddleware.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FBillingMiddleware.php&line=21", "ajax": false, "filename": "BillingMiddleware.php", "line": "21"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.101, "width_percent": 3.188}, {"sql": "select * from `brands` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 527}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 595}, {"index": 18, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.013199, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:527", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 527}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=527", "ajax": false, "filename": "BulkEdit.php", "line": "527"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.289, "width_percent": 1.113}, {"sql": "select * from `invites` where `organization_id_sender` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 528}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 595}, {"index": 18, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0206969, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:528", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 528}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=528", "ajax": false, "filename": "BulkEdit.php", "line": "528"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.403, "width_percent": 1.231}, {"sql": "select * from `categories` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 529}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 595}, {"index": 18, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.025275, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:529", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 529}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=529", "ajax": false, "filename": "BulkEdit.php", "line": "529"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.634, "width_percent": 1.164}, {"sql": "select * from `channels` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 530}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 595}, {"index": 18, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0300689, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:530", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 530}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=530", "ajax": false, "filename": "BulkEdit.php", "line": "530"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.798, "width_percent": 1.889}, {"sql": "select `id`, `name` from `families` where `organization_id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 602}, {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.035989, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:602", "source": {"index": 15, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 602}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=602", "ajax": false, "filename": "BulkEdit.php", "line": "602"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.688, "width_percent": 1.265}, {"sql": "select `attributes`.`id`, `attributes`.`attribute_type_id`, `attributes`.`name`, `attributes`.`handle`, `attributes`.`rules`, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 602}, {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.041857, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:602", "source": {"index": 19, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 602}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=602", "ajax": false, "filename": "BulkEdit.php", "line": "602"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 59.953, "width_percent": 1.35}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 602}, {"index": 25, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 680}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.047486, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:602", "source": {"index": 24, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 602}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=602", "ajax": false, "filename": "BulkEdit.php", "line": "602"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.302, "width_percent": 1.231}, {"sql": "select * from `filters` where `type` = 'bulk' and `organization_id` = 1 and `user_id` = 1 limit 1", "type": "query", "params": [], "bindings": ["bulk", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 630}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 681}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.052776, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:630", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 630}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=630", "ajax": false, "filename": "BulkEdit.php", "line": "630"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.534, "width_percent": 1.383}, {"sql": "select count(*) as aggregate from `products` where `id` in ('1') and `organization_id` = 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 18, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.056612, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 63.917, "width_percent": 1.096}, {"sql": "select `id`, `sku` from `products` where `id` in ('1') and `organization_id` = 1 order by `id` desc limit 15 offset 0", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 18, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.060172, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 16, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 65.013, "width_percent": 1.08}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.064262, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.093, "width_percent": 1.451}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.068017, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 67.544, "width_percent": 1.619}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at`, `invite_product`.`id` as `pivotId` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.072991, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.163, "width_percent": 2.379}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` in (1) and `versions`.`id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.078016, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.542, "width_percent": 2.092}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0822139, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 73.634, "width_percent": 1.316}, {"sql": "select * from `variants` where `variants`.`product_id` in (1) and `version_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 23, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.0879378, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 21, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.949, "width_percent": 1.265}, {"sql": "select `inventories`.`id`, `inventories`.`location_id`, `inventories`.`available_quantity`, `inventories`.`variant_id` from `inventories` where `inventories`.`variant_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 28, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.092502, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 26, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.215, "width_percent": 2.901}, {"sql": "select * from `product_variant_settings` where `product_variant_settings`.`morphable_id` in (1) and `product_variant_settings`.`morphable_type` = 'App\\\\Models\\\\Product\\\\Variant'", "type": "query", "params": [], "bindings": ["App\\Models\\Product\\Variant"], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 28, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 29, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.099803, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 26, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.116, "width_percent": 1.231}, {"sql": "select `files`.*, `file_product`.`product_id` as `pivot_product_id`, `file_product`.`file_id` as `pivot_file_id`, `file_product`.`uploaded_for` as `pivot_uploaded_for`, `file_product`.`created_at` as `pivot_created_at`, `file_product`.`updated_at` as `pivot_updated_at` from `files` inner join `file_product` on `files`.`id` = `file_product`.`file_id` where `file_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.105659, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.348, "width_percent": 1.282}, {"sql": "select * from `inventories` where `variant_id` is null and `inventories`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 23, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.109125, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 21, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.63, "width_percent": 2.058}, {"sql": "select `files`.*, `file_product`.`product_id` as `laravel_through_key` from `files` inner join `file_product` on `file_product`.`file_id` = `files`.`id` where `file_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 22, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.114693, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "BulkEdit.php:714", "source": {"index": 20, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 714}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FClasses%2FBulk%2FBulkEdit.php&line=714", "ajax": false, "filename": "BulkEdit.php", "line": "714"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.688, "width_percent": 1.282}, {"sql": "select * from `families` where exists (select * from `attribute_family_product_versions` where `families`.`id` = `attribute_family_product_versions`.`family_id` and `product_id` = 1 and `version_id` = 1) order by `id` asc", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.120425, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.97, "width_percent": 1.417}, {"sql": "select `attributes`.*, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 21, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.124635, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.387, "width_percent": 1.552}, {"sql": "select * from `attribute_types` where `attribute_types`.`id` in (1, 3, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 26, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.12992, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.939, "width_percent": 1.08}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 26, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 27, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.133006, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.018, "width_percent": 1.063}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 1 and `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.136749, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.081, "width_percent": 1.35}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 3 and `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 3, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1407309, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.43, "width_percent": 1.316}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 4 and `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 4, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1452758, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.746, "width_percent": 1.265}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 5 and `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 5, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.149041, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.011, "width_percent": 1.164}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 6 and `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 6, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.153757, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.175, "width_percent": 1.198}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 7 and `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 7, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1094}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 686}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.1579392, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.373, "width_percent": 1.046}, {"sql": "select * from `organizations` where `id` = 1 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 201}, {"index": 17, "namespace": null, "name": "app/Classes/Bulk/BulkEdit.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Classes\\Bulk\\BulkEdit.php", "line": 765}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/BulkEditController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\BulkEditController.php", "line": 246}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.16521, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "User.php:201", "source": {"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=201", "ajax": false, "filename": "User.php", "line": "201"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.419, "width_percent": 1.282}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and `organizations`.`id` = 1 and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 81}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "start": **********.1734128, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "AppServiceProvider.php:51", "source": {"index": 17, "namespace": null, "name": "app/Providers/AppServiceProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Providers\\AppServiceProvider.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FProviders%2FAppServiceProvider.php&line=51", "ajax": false, "filename": "AppServiceProvider.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.701, "width_percent": 1.299}]}, "models": {"data": {"App\\Models\\Product\\Attribute": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Product\\AttributeFamilyProductVersion": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=1", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Organization\\Organization": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Product\\AttributeType": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeType.php&line=1", "ajax": false, "filename": "AttributeType.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Channel\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "App\\Models\\Product\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}, "App\\Models\\Product\\Variant": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVariant.php&line=1", "ajax": false, "filename": "Variant.php", "line": "?"}}}, "count": 32, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/dashboard\"\n]", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "bulk_edit_data": "array:3 [\n  \"target_page\" => 1\n  \"version_id\" => \"1\"\n  \"productIds\" => array:1 [\n    0 => \"1\"\n  ]\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/products/fetch/bulk/edit", "action_name": "fetch.bulk.edit.products", "controller_action": "App\\Http\\Controllers\\Product\\BulkEditController@fetch_apimio_products", "uri": "POST products/fetch/bulk/edit", "controller": "App\\Http\\Controllers\\Product\\BulkEditController@fetch_apimio_products<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FBulkEditController.php&line=222\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers\\Product", "prefix": "/products", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FBulkEditController.php&line=222\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/BulkEditController.php:222-252</a>", "middleware": "web, check_billing", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4af49b-6a8f-4d23-ad19-41648db63b1a\" target=\"_blank\">View in Telescope</a>", "duration": "837ms", "peak_memory": "36MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-548670119 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-548670119\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1812491062 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>productIds</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>bulk_edit_filter_products</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>bulk_edit_version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1812491062\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-509186600 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">133</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/products?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImZRSVQrVFk5Ti9peE5zdXpmVlF3YXc9PSIsInZhbHVlIjoibzhlVEo3UTVtTXlFTGl0NWs4bkk0L0IyVHA0aTh4L29lREh5QUljcjcxUXRLOVMxR0JzaGpKYk9UTm03MlRHNW13RVcwdjVVL2UrTjJaYkN1a3lIcnVWbWdFbGI5eTZmWFdwL3JoM0JOSTY2N1JORnpvSTY2dnBaRkdBTmREcDUiLCJtYWMiOiI3ZGQwZThkNmZmZjIwNTA3NGQ2M2Q5OTYyYjRmM2Y4MTg5ZmFlM2RjY2M5YTcyY2UzMGNjYmFiOWJkMjUyYWFiIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkhQekxIbGlNZWZBczhnWUsyRzRzd1E9PSIsInZhbHVlIjoieDFXQmRwSmdKMW5ocC9OYjFKTGRzbXgyZEUzWWRrRlpSRkhLbHFiODhpbHlPamx2SXh4SlRxT1VJYjV2UmdIbGl2NEpGMHpVd1hSQkkwdWdNcElhVmRmemh6all6RXpPQzdJNGZVZWZHa2dMVERhc3ljL2ZxeFRGck1PRFJYZUgiLCJtYWMiOiIzZjdmNzhiNTg4ODQwNTRlNmQ1YjVmYjkzOGMxOGRkYjkzYmVhOGJkMTIwMjg4YWVjOGE5MjY0YjcwM2NjMTgyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-509186600\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1065070812 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zvOZSvHzZnsYe7BByH4nzLoQwwGRhsRK0dRNgSNd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1065070812\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1953059706 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:53:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1953059706\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1431152671 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"30 characters\">http://localhost:8000/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>bulk_edit_data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>target_page</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>productIds</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1431152671\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/products/fetch/bulk/edit", "action_name": "fetch.bulk.edit.products", "controller_action": "App\\Http\\Controllers\\Product\\BulkEditController@fetch_apimio_products"}, "badge": null}}