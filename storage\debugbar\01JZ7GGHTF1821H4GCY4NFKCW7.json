{"__meta": {"id": "01JZ7GGHTF1821H4GCY4NFKCW7", "datetime": "2025-07-03 06:45:15", "utime": **********.729084, "method": "PATCH", "uri": "/products/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 9, "messages": [{"message": "[06:45:15] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.307269, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: substr(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php on line 511", "message_html": null, "is_string": false, "label": "warning", "time": **********.36633, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: strip_tags(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php on line 251", "message_html": null, "is_string": false, "label": "warning", "time": **********.537615, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: Function utf8_encode() is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php on line 256", "message_html": null, "is_string": false, "label": "warning", "time": **********.537828, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: mb_detect_encoding(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php on line 211", "message_html": null, "is_string": false, "label": "warning", "time": **********.550226, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: Function utf8_encode() is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php on line 213", "message_html": null, "is_string": false, "label": "warning", "time": **********.550448, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: utf8_encode(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php on line 213", "message_html": null, "is_string": false, "label": "warning", "time": **********.550735, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.info: channel location not found in inventory created event  location id = 1", "message_html": null, "is_string": false, "label": "info", "time": **********.626696, "xdebug_link": null, "collector": "log"}, {"message": "[06:45:15] LOG.warning: Optional parameter $type declared before required parameter $pattern is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\Rules\\Validation.php on line 129", "message_html": null, "is_string": false, "label": "warning", "time": **********.652604, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751525114.494559, "end": **********.7293, "duration": 1.234740972518921, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1751525114.494559, "relative_start": 0, "end": **********.155014, "relative_end": **********.155014, "duration": 0.****************, "duration_str": "660ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.155028, "relative_start": 0.****************, "end": **********.729305, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "574ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.174969, "relative_start": 0.****************, "end": **********.179902, "relative_end": **********.179902, "duration": 0.*****************, "duration_str": "4.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.725148, "relative_start": 1.****************, "end": **********.725773, "relative_end": **********.725773, "duration": 0.0006251335144042969, "duration_str": "625μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "PUT products/{product}", "middleware": "web, check_session, auth, verified, activeOrganization", "as": "products.update", "controller": "App\\Http\\Controllers\\Product\\ProductController@update<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=613\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=613\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:613-632</a>"}, "queries": {"count": 81, "nb_statements": 81, "nb_visible_statements": 81, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12903, "accumulated_duration_str": "129ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.210776, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 2.317}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.222425, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 2.317, "width_percent": 0.667}, {"sql": "select * from `products` where `products`.`id` = '1' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 281}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.243121, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "Product.php:281", "source": {"index": 18, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 281}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=281", "ajax": false, "filename": "Product.php", "line": "281"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 2.984, "width_percent": 0.45}, {"sql": "update `products` set `status` = '1', `products`.`updated_at` = '2025-07-03 06:45:15' where `id` = 1", "type": "query", "params": [], "bindings": ["1", "2025-07-03 06:45:15", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 301}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.2484329, "duration": 0.00422, "duration_str": "4.22ms", "memory": 0, "memory_str": null, "filename": "Product.php:301", "source": {"index": 14, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 301}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=301", "ajax": false, "filename": "Product.php", "line": "301"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 3.433, "width_percent": 3.271}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` = 1 and `version_id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Events/Product/ManageAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Events\\Product\\ManageAttributes.php", "line": 28}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 319}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.264589, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ManageAttributes.php:28", "source": {"index": 16, "namespace": null, "name": "app/Events/Product/ManageAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Events\\Product\\ManageAttributes.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FEvents%2FProduct%2FManageAttributes.php&line=28", "ajax": false, "filename": "ManageAttributes.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 6.704, "width_percent": 0.698}, {"sql": "select * from `brand_product` where `brand_product`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageBrandListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageBrandListener.php", "line": 47}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.272054, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ManageBrandListener.php:47", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageBrandListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageBrandListener.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageBrandListener.php&line=47", "ajax": false, "filename": "ManageBrandListener.php", "line": "47"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.401, "width_percent": 0.419}, {"sql": "insert into `brand_product` (`brand_id`, `created_at`, `product_id`, `updated_at`) values (2, '2025-07-03 06:45:15', 1, '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": [2, "2025-07-03 06:45:15", 1, "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageBrandListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageBrandListener.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.27615, "duration": 0.00505, "duration_str": "5.05ms", "memory": 0, "memory_str": null, "filename": "ManageBrandListener.php:47", "source": {"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageBrandListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageBrandListener.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageBrandListener.php&line=47", "ajax": false, "filename": "ManageBrandListener.php", "line": "47"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.82, "width_percent": 3.914}, {"sql": "select * from `category_product` where `category_product`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageCategoryListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageCategoryListener.php", "line": 55}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2867608, "duration": 0.00605, "duration_str": "6.05ms", "memory": 0, "memory_str": null, "filename": "ManageCategoryListener.php:55", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageCategoryListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageCategoryListener.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageCategoryListener.php&line=55", "ajax": false, "filename": "ManageCategoryListener.php", "line": "55"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 11.734, "width_percent": 4.689}, {"sql": "insert into `category_product` (`category_id`, `created_at`, `product_id`, `updated_at`) values (1, '2025-07-03 06:45:15', 1, '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": [1, "2025-07-03 06:45:15", 1, "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageCategoryListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageCategoryListener.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.2970982, "duration": 0.00466, "duration_str": "4.66ms", "memory": 0, "memory_str": null, "filename": "ManageCategoryListener.php:55", "source": {"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageCategoryListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageCategoryListener.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageCategoryListener.php&line=55", "ajax": false, "filename": "ManageCategoryListener.php", "line": "55"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.423, "width_percent": 3.612}, {"sql": "select * from `invite_product` where `invite_product`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageInvitesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageInvitesListener.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.30816, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ManageInvitesListener.php:48", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageInvitesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageInvitesListener.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageInvitesListener.php&line=48", "ajax": false, "filename": "ManageInvitesListener.php", "line": "48"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.034, "width_percent": 0.419}, {"sql": "insert into `invite_product` (`created_at`, `invite_id`, `product_id`, `updated_at`) values ('2025-07-03 06:45:15', 1, 1, '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": ["2025-07-03 06:45:15", 1, 1, "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageInvitesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageInvitesListener.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.31131, "duration": 0.0045899999999999995, "duration_str": "4.59ms", "memory": 0, "memory_str": null, "filename": "ManageInvitesListener.php:48", "source": {"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageInvitesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageInvitesListener.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageInvitesListener.php&line=48", "ajax": false, "filename": "ManageInvitesListener.php", "line": "48"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.453, "width_percent": 3.557}, {"sql": "select * from `channel_product` where `channel_product`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageChannelsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageChannelsListener.php", "line": 52}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.320401, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "ManageChannelsListener.php:52", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageChannelsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageChannelsListener.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageChannelsListener.php&line=52", "ajax": false, "filename": "ManageChannelsListener.php", "line": "52"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.01, "width_percent": 0.69}, {"sql": "insert into `channel_product` (`channel_id`, `created_at`, `product_id`, `updated_at`) values (1, '2025-07-03 06:45:15', 1, '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": [1, "2025-07-03 06:45:15", 1, "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageChannelsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageChannelsListener.php", "line": 52}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.324614, "duration": 0.0041600000000000005, "duration_str": "4.16ms", "memory": 0, "memory_str": null, "filename": "ManageChannelsListener.php:52", "source": {"index": 13, "namespace": null, "name": "app/Listeners/Product/ManageChannelsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageChannelsListener.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageChannelsListener.php&line=52", "ajax": false, "filename": "ManageChannelsListener.php", "line": "52"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.7, "width_percent": 3.224}, {"sql": "select * from `product_version` where `product_version`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Listeners/Product/ManageVersionListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageVersionListener.php", "line": 57}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.33478, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ManageVersionListener.php:57", "source": {"index": 16, "namespace": null, "name": "app/Listeners/Product/ManageVersionListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageVersionListener.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageVersionListener.php&line=57", "ajax": false, "filename": "ManageVersionListener.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.924, "width_percent": 0.504}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageVersionListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageVersionListener.php", "line": 62}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3388321, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ManageVersionListener.php:62", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/ManageVersionListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageVersionListener.php", "line": 62}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageVersionListener.php&line=62", "ajax": false, "filename": "ManageVersionListener.php", "line": "62"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.427, "width_percent": 0.519}, {"sql": "select count(*) as aggregate from `variants` where `variants`.`version_id` = 1 and `variants`.`version_id` is not null and `product_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Listeners/Product/ManageVersionListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageVersionListener.php", "line": 63}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3447878, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ManageVersionListener.php:63", "source": {"index": 19, "namespace": null, "name": "app/Listeners/Product/ManageVersionListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageVersionListener.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageVersionListener.php&line=63", "ajax": false, "filename": "ManageVersionListener.php", "line": "63"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.947, "width_percent": 0.457}, {"sql": "select * from `attribute_family_product_versions` where `product_version_id` = 1 and `attribute_family_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 503}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.349788, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Product.php:503", "source": {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 503}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=503", "ajax": false, "filename": "Product.php", "line": "503"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.404, "width_percent": 0.721}, {"sql": "select * from `attribute_family` where `attribute_family`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 523}, {"index": 21, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.355118, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Product.php:523", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 523}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=523", "ajax": false, "filename": "Product.php", "line": "523"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.125, "width_percent": 0.442}, {"sql": "select * from `product_version` where `product_version`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 525}, {"index": 21, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.3585, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Product.php:525", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 525}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=525", "ajax": false, "filename": "Product.php", "line": "525"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.567, "width_percent": 0.426}, {"sql": "select * from `attribute_family_product_versions` where `product_version_id` = 1 and `attribute_family_id` = 2 limit 1", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 503}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.361995, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "Product.php:503", "source": {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 503}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=503", "ajax": false, "filename": "Product.php", "line": "503"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.993, "width_percent": 0.907}, {"sql": "select * from `attribute_family` where `attribute_family`.`id` = 2 limit 1", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 523}, {"index": 21, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.367276, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "Product.php:523", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 523}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=523", "ajax": false, "filename": "Product.php", "line": "523"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.9, "width_percent": 0.876}, {"sql": "select * from `product_version` where `product_version`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 525}, {"index": 21, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.37124, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:525", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 525}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=525", "ajax": false, "filename": "Product.php", "line": "525"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.775, "width_percent": 0.465}, {"sql": "insert into `attribute_family_product_versions` (`attribute_family_id`, `value`, `product_version_id`, `attribute_id`, `family_id`, `product_id`, `version_id`, `updated_at`, `created_at`) values (2, '', 1, 3, 1, 1, 1, '2025-07-03 06:45:15', '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": [2, "", 1, 3, 1, 1, 1, "2025-07-03 06:45:15", "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 531}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.374596, "duration": 0.00452, "duration_str": "4.52ms", "memory": 0, "memory_str": null, "filename": "Product.php:531", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 531}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=531", "ajax": false, "filename": "Product.php", "line": "531"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.24, "width_percent": 3.503}, {"sql": "select * from `product_version` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Product/AttributeFamilyProductVersion.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\AttributeFamilyProductVersion.php", "line": 19}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 531}, {"index": 24, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 30, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.384705, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "AttributeFamilyProductVersion.php:19", "source": {"index": 16, "namespace": null, "name": "app/Models/Product/AttributeFamilyProductVersion.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\AttributeFamilyProductVersion.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=19", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "19"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.743, "width_percent": 0.372}, {"sql": "select * from `versions` where `versions`.`id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Product/AttributeFamilyProductVersion.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\AttributeFamilyProductVersion.php", "line": 19}, {"index": 28, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 531}, {"index": 29, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 35, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.388158, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "AttributeFamilyProductVersion.php:19", "source": {"index": 21, "namespace": null, "name": "app/Models/Product/AttributeFamilyProductVersion.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\AttributeFamilyProductVersion.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=19", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "19"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.115, "width_percent": 0.341}, {"sql": "select * from `attributes` where exists (select * from `attribute_family` where `attributes`.`id` = `attribute_family`.`attribute_id` and `attribute_family`.`id` = 2) and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [2, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Product/AttributeFamilyProductVersion.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\AttributeFamilyProductVersion.php", "line": 23}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 531}, {"index": 24, "namespace": null, "name": "app/Listeners/Product/ManageAttributesListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageAttributesListener.php", "line": 72}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 30, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.39241, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "AttributeFamilyProductVersion.php:23", "source": {"index": 16, "namespace": null, "name": "app/Models/Product/AttributeFamilyProductVersion.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\AttributeFamilyProductVersion.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=23", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "23"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.456, "width_percent": 0.434}, {"sql": "select `id`, `handle` from `attributes` where `handle` in ('seo_url', 'seo_title', 'seo_description', 'seo_keyword') and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["seo_url", "seo_title", "seo_description", "seo_keyword", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.397883, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ManageSeoFieldsListener.php:34", "source": {"index": 14, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageSeoFieldsListener.php&line=34", "ajax": false, "filename": "ManageSeoFieldsListener.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.89, "width_percent": 0.45}, {"sql": "select `id`, `attribute_id` from `attribute_family` where `attribute_id` in (4, 5, 6, 7)", "type": "query", "params": [], "bindings": [4, 5, 6, 7], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.408686, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageSeoFieldsListener.php:35", "source": {"index": 14, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FManageSeoFieldsListener.php&line=35", "ajax": false, "filename": "ManageSeoFieldsListener.php", "line": "35"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.34, "width_percent": 0.853}, {"sql": "select * from `products` where `products`.`id` = 1 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 19, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.4157171, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 18, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.192, "width_percent": 0.783}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 23, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.420579, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.975, "width_percent": 0.535}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 23, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.424224, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.51, "width_percent": 0.473}, {"sql": "select `vendors`.*, `product_vendor`.`product_id` as `pivot_product_id`, `product_vendor`.`vendor_id` as `pivot_vendor_id` from `vendors` inner join `product_vendor` on `vendors`.`id` = `product_vendor`.`vendor_id` where `product_vendor`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 23, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.428333, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.983, "width_percent": 1.674}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 23, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.435487, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.657, "width_percent": 0.527}, {"sql": "select * from `variants` where `variants`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 24, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 30, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 31, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.438956, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 23, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.184, "width_percent": 0.589}, {"sql": "select `files`.*, `file_product`.`product_id` as `pivot_product_id`, `file_product`.`file_id` as `pivot_file_id`, `file_product`.`uploaded_for` as `pivot_uploaded_for`, `file_product`.`created_at` as `pivot_created_at`, `file_product`.`updated_at` as `pivot_updated_at` from `files` inner join `file_product` on `files`.`id` = `file_product`.`file_id` where `file_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 23, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.443759, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.773, "width_percent": 0.442}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` in (1) and `version_id` in ('1')", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, {"index": 23, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.45044, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:32", "source": {"index": 22, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=32", "ajax": false, "filename": "ProductTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 44.215, "width_percent": 0.659}, {"sql": "select `attribute_family_id` from `attribute_family_product_versions` where `product_version_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1043}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.453443, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Product.php:1043", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1043}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1043", "ajax": false, "filename": "Product.php", "line": "1043"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 44.873, "width_percent": 0.426}, {"sql": "select `family_id` from `attribute_family` where `id` in (1, 3, 5, 4, 6, 2)", "type": "query", "params": [], "bindings": [1, 3, 5, 4, 6, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1045}, {"index": 15, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.457, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:1045", "source": {"index": 14, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1045}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1045", "ajax": false, "filename": "Product.php", "line": "1045"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.3, "width_percent": 0.388}, {"sql": "select `id` from `families` where `organization_id` = 1 and `is_default` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1048}, {"index": 15, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.460629, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "Product.php:1048", "source": {"index": 14, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1048}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1048", "ajax": false, "filename": "Product.php", "line": "1048"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.687, "width_percent": 0.349}, {"sql": "select * from `families` where `organization_id` = 1 and `families`.`id` in (1, 1, 2, 2, 2, 2, 1, 2)", "type": "query", "params": [], "bindings": [1, 1, 1, 2, 2, 2, 2, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.468889, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:1054", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1054", "ajax": false, "filename": "Product.php", "line": "1054"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 46.036, "width_percent": 0.465}, {"sql": "select `attributes`.*, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, {"index": 20, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 21, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 27, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.472773, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Product.php:1054", "source": {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1054", "ajax": false, "filename": "Product.php", "line": "1054"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 46.501, "width_percent": 0.527}, {"sql": "select * from `attribute_types` where `attribute_types`.`id` in (1, 3, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, {"index": 25, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 26, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 32, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.476263, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "Product.php:1054", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1054", "ajax": false, "filename": "Product.php", "line": "1054"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.028, "width_percent": 0.651}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, {"index": 25, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 26, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 32, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.484231, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Product.php:1054", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1054}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1054", "ajax": false, "filename": "Product.php", "line": "1054"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.679, "width_percent": 0.388}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `attribute_family_id` = 1 and `product_version_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.4875011, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "Product.php:1060", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1060", "ajax": false, "filename": "Product.php", "line": "1060"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 48.066, "width_percent": 0.891}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `attribute_family_id` = 2 and `product_version_id` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.49183, "duration": 0.0019, "duration_str": "1.9ms", "memory": 0, "memory_str": null, "filename": "Product.php:1060", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1060", "ajax": false, "filename": "Product.php", "line": "1060"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 48.958, "width_percent": 1.473}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `attribute_family_id` = 3 and `product_version_id` = 1", "type": "query", "params": [], "bindings": [3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.5028038, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "Product.php:1060", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1060", "ajax": false, "filename": "Product.php", "line": "1060"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.43, "width_percent": 0.868}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `attribute_family_id` = 4 and `product_version_id` = 1", "type": "query", "params": [], "bindings": [4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.5082822, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Product.php:1060", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1060", "ajax": false, "filename": "Product.php", "line": "1060"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 51.298, "width_percent": 0.76}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `attribute_family_id` = 5 and `product_version_id` = 1", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.513994, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "Product.php:1060", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1060", "ajax": false, "filename": "Product.php", "line": "1060"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.058, "width_percent": 1.504}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `attribute_family_id` = 6 and `product_version_id` = 1", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 33}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 71}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}], "start": **********.518864, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Product.php:1060", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1060}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1060", "ajax": false, "filename": "Product.php", "line": "1060"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.561, "width_percent": 0.705}, {"sql": "select `id` from `attributes` where (`handle` = 'seo_description' and `organization_id` = 1) and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["seo_description", 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 225}, {"index": 18, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 78}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.523405, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:225", "source": {"index": 17, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=225", "ajax": false, "filename": "ProductTrait.php", "line": "225"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.266, "width_percent": 0.496}, {"sql": "select * from `attribute_family` where `attribute_id` = 6 limit 1", "type": "query", "params": [], "bindings": [6], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 226}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 78}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.5270991, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:226", "source": {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 226}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=226", "ajax": false, "filename": "ProductTrait.php", "line": "226"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.762, "width_percent": 0.535}, {"sql": "select * from `attribute_family_product_versions` where `attribute_family_id` = 5 and `product_version_id` = 1 limit 1", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 238}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 78}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.53405, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:238", "source": {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 238}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=238", "ajax": false, "filename": "ProductTrait.php", "line": "238"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.297, "width_percent": 0.899}, {"sql": "select `id` from `attributes` where (`handle` = 'seo_keyword' and `organization_id` = 1) and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["seo_keyword", 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 182}, {"index": 18, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.5395188, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:182", "source": {"index": 17, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 182}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=182", "ajax": false, "filename": "ProductTrait.php", "line": "182"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.196, "width_percent": 0.442}, {"sql": "select * from `attribute_family` where `attribute_id` = 7 limit 1", "type": "query", "params": [], "bindings": [7], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 185}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 84}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.542373, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:185", "source": {"index": 16, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=185", "ajax": false, "filename": "ProductTrait.php", "line": "185"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.638, "width_percent": 0.38}, {"sql": "select * from `attribute_family_product_versions` where `attribute_family_id` = 6 and `product_version_id` = 1 and (`attribute_family_id` = 6 and `product_version_id` = 1 and `attribute_id` = 7 and `family_id` = 2 and `version_id` = 1 and `product_id` = 1) limit 1", "type": "query", "params": [], "bindings": [6, 1, 6, 1, 7, 2, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 201}, {"index": 18, "namespace": null, "name": "app/Listeners/Product/ManageSeoFieldsListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\ManageSeoFieldsListener.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.546194, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "ProductTrait.php:201", "source": {"index": 17, "namespace": null, "name": "app/Traits/Product/ProductTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Product\\ProductTrait.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FProduct%2FProductTrait.php&line=201", "ajax": false, "filename": "ProductTrait.php", "line": "201"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 57.018, "width_percent": 1.039}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Listeners/ChannelUpdateStatusListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\ChannelUpdateStatusListener.php", "line": 31}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.552376, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ChannelUpdateStatusListener.php:31", "source": {"index": 19, "namespace": null, "name": "app/Listeners/ChannelUpdateStatusListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\ChannelUpdateStatusListener.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FChannelUpdateStatusListener.php&line=31", "ajax": false, "filename": "ChannelUpdateStatusListener.php", "line": "31"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.056, "width_percent": 0.519}, {"sql": "select * from `channel_product_statuses` where (`channel_product_id` = 5 and `organization_id` = 1 and `type` = 'shopify') and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [5, 1, "shopify", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Listeners/ChannelUpdateStatusListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\ChannelUpdateStatusListener.php", "line": 40}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.556996, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "ChannelUpdateStatusListener.php:40", "source": {"index": 16, "namespace": null, "name": "app/Listeners/ChannelUpdateStatusListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\ChannelUpdateStatusListener.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FChannelUpdateStatusListener.php&line=40", "ajax": false, "filename": "ChannelUpdateStatusListener.php", "line": "40"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.576, "width_percent": 2.046}, {"sql": "insert into `channel_product_statuses` (`response`, `channel_product_id`, `organization_id`, `type`, `status`, `updated_at`, `created_at`) values (null, 5, '1', 'shopify', 0, '2025-07-03 06:45:15', '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": [null, 5, "1", "shopify", 0, "2025-07-03 06:45:15", "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/ChannelUpdateStatusListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\ChannelUpdateStatusListener.php", "line": 51}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 333}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.563718, "duration": 0.00967, "duration_str": "9.67ms", "memory": 0, "memory_str": null, "filename": "ChannelUpdateStatusListener.php:51", "source": {"index": 15, "namespace": null, "name": "app/Listeners/ChannelUpdateStatusListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\ChannelUpdateStatusListener.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FChannelUpdateStatusListener.php&line=51", "ajax": false, "filename": "ChannelUpdateStatusListener.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.622, "width_percent": 7.494}, {"sql": "select * from `variants` where `variants`.`product_id` = 1 and `variants`.`product_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 386}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.586297, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "Product.php:386", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 386}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=386", "ajax": false, "filename": "Product.php", "line": "386"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 68.116, "width_percent": 1.457}, {"sql": "select * from `channel_location` where `channel_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 388}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.595276, "duration": 0.00783, "duration_str": "7.83ms", "memory": 0, "memory_str": null, "filename": "Product.php:388", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 388}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=388", "ajax": false, "filename": "Product.php", "line": "388"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.573, "width_percent": 6.068}, {"sql": "select * from `inventories` where (`organization_id` = 1 and `location_id` = 1 and `variant_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 394}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.609641, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "Product.php:394", "source": {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 394}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=394", "ajax": false, "filename": "Product.php", "line": "394"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.641, "width_percent": 1.023}, {"sql": "insert into `inventories` (`product_id`, `organization_id`, `location_id`, `variant_id`, `updated_at`, `created_at`) values (1, 1, 1, 1, '2025-07-03 06:45:15', '2025-07-03 06:45:15')", "type": "query", "params": [], "bindings": [1, 1, 1, 1, "2025-07-03 06:45:15", "2025-07-03 06:45:15"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 396}, {"index": 22, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.617561, "duration": 0.00436, "duration_str": "4.36ms", "memory": 0, "memory_str": null, "filename": "Product.php:396", "source": {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 396}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=396", "ajax": false, "filename": "Product.php", "line": "396"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.664, "width_percent": 3.379}, {"sql": "select * from `channel_location` where `channel_location`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Inventory.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Inventory.php", "line": 40}, {"index": 33, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 396}, {"index": 34, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 35, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 36, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.624217, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "Inventory.php:40", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Inventory.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Inventory.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FInventory.php&line=40", "ajax": false, "filename": "Inventory.php", "line": "40"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.043, "width_percent": 0.364}, {"sql": "select * from `channel_location` where `location_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Inventory.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Inventory.php", "line": 44}, {"index": 28, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 396}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.627126, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Inventory.php:44", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Inventory.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Inventory.php", "line": 44}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FInventory.php&line=44", "ajax": false, "filename": "Inventory.php", "line": "44"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.408, "width_percent": 0.566}, {"sql": "select * from `inventories` where (`variant_id` = 1 and `location_id` = 1)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Inventory.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Inventory.php", "line": 48}, {"index": 28, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 396}, {"index": 29, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 337}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 31, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6323, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Inventory.php:48", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Inventory.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Inventory.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FInventory.php&line=48", "ajax": false, "filename": "Inventory.php", "line": "48"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.973, "width_percent": 0.806}, {"sql": "select * from `channel_product` where (`channel_id` = '1' and `product_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 341}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.636497, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:341", "source": {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 341}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=341", "ajax": false, "filename": "Product.php", "line": "341"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.779, "width_percent": 0.395}, {"sql": "select * from `channel_product_statuses` where (`channel_product_id` = 5 and `organization_id` = 1 and `type` = 'shopify') and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [5, 1, "shopify", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 344}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.641022, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "Product.php:344", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 344}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=344", "ajax": false, "filename": "Product.php", "line": "344"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.175, "width_percent": 0.574}, {"sql": "select * from `variants` where `variants`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 413}, {"index": 21, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 368}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.64503, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Product.php:413", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 413}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=413", "ajax": false, "filename": "Product.php", "line": "413"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.748, "width_percent": 0.488}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` = 1 and `versions`.`id` in ('1')", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 48}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.653351, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:48", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=48", "ajax": false, "filename": "CalculateScoreListener.php", "line": "48"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.236, "width_percent": 0.535}, {"sql": "select `id` from `families` where `organization_id` = 1 and `is_default` = 1 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 97}, {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 52}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6567402, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:97", "source": {"index": 14, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=97", "ajax": false, "filename": "CalculateScoreListener.php", "line": "97"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.771, "width_percent": 0.357}, {"sql": "select distinct `attribute_family_product_versions`.`family_id` from `attribute_family_product_versions` where `version_id` = 1 and `product_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 105}, {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 52}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.659727, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:105", "source": {"index": 14, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 105}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=105", "ajax": false, "filename": "CalculateScoreListener.php", "line": "105"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.128, "width_percent": 0.372}, {"sql": "select `attribute_id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 111}, {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 52}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.666338, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:111", "source": {"index": 14, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=111", "ajax": false, "filename": "CalculateScoreListener.php", "line": "111"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.5, "width_percent": 0.884}, {"sql": "select `attribute_family_product_versions`.`attribute_id`, `attributes`.`attribute_type_id`, `attributes`.`rules`, `value`, `attribute_family_product_versions`.`unit`, `attribute_family_product_versions`.`family_id` from `attribute_family_product_versions` left join `attributes` on `attribute_family_product_versions`.`attribute_id` = `attributes`.`id` where `version_id` = 1 and `product_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 118}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 52}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.67085, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:118", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=118", "ajax": false, "filename": "CalculateScoreListener.php", "line": "118"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.383, "width_percent": 0.488}, {"sql": "select * from `attributes` where `organization_id` = 1 and `id` in (1, 3, 4, 5, 6, 7) and `id` not in (1, 4, 6, 5, 7, 3) and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, 1, 3, 4, 5, 6, 7, 1, 4, 6, 5, 7, 3, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 149}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 52}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.674466, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:149", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 149}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=149", "ajax": false, "filename": "CalculateScoreListener.php", "line": "149"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.872, "width_percent": 0.473}, {"sql": "select * from `variants` where `variants`.`product_id` = 1 and `variants`.`product_id` is not null and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 199}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.68019, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:199", "source": {"index": 16, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=199", "ajax": false, "filename": "CalculateScoreListener.php", "line": "199"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.344, "width_percent": 1.62}, {"sql": "select * from `settings` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 205}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.686976, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:286", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 286}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=286", "ajax": false, "filename": "CalculateScoreListener.php", "line": "286"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.964, "width_percent": 0.504}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 241}, {"index": 20, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.69138, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:241", "source": {"index": 19, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=241", "ajax": false, "filename": "CalculateScoreListener.php", "line": "241"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.468, "width_percent": 0.434}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 241}, {"index": 20, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6961162, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:241", "source": {"index": 19, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=241", "ajax": false, "filename": "CalculateScoreListener.php", "line": "241"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.902, "width_percent": 1.713}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` in (1)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 241}, {"index": 20, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.701334, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:241", "source": {"index": 19, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 241}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=241", "ajax": false, "filename": "CalculateScoreListener.php", "line": "241"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.615, "width_percent": 0.481}, {"sql": "select * from `settings` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 286}, {"index": 16, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 259}, {"index": 17, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}], "start": **********.7043362, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:286", "source": {"index": 15, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 286}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=286", "ajax": false, "filename": "CalculateScoreListener.php", "line": "286"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.095, "width_percent": 0.38}, {"sql": "update `product_version` set `score` = 64, `product_version`.`updated_at` = '2025-07-03 06:45:15' where `product_id` = 1 and `version_id` = 1", "type": "query", "params": [], "bindings": [64, "2025-07-03 06:45:15", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 69}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/helpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php", "line": 433}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 618}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.707649, "duration": 0.011, "duration_str": "11ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreListener.php:69", "source": {"index": 12, "namespace": null, "name": "app/Listeners/Product/CalculateScoreListener.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\Product\\CalculateScoreListener.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FProduct%2FCalculateScoreListener.php&line=69", "ajax": false, "filename": "CalculateScoreListener.php", "line": "69"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.475, "width_percent": 8.525}]}, "models": {"data": {"App\\Models\\Product\\AttributeFamilyProductVersion": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=1", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "?"}}, "App\\Models\\Setting": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 13, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}, "App\\Models\\Product\\AttributeFamily": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "App\\Models\\Product\\Variant": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVariant.php&line=1", "ajax": false, "filename": "Variant.php", "line": "?"}}, "App\\Models\\Product\\ProductVersion": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProductVersion.php&line=1", "ajax": false, "filename": "ProductVersion.php", "line": "?"}}, "App\\Models\\Product\\AttributeType": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeType.php&line=1", "ajax": false, "filename": "AttributeType.php", "line": "?"}}, "App\\Models\\Channel\\ChannelLocation": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannelLocation.php&line=1", "ajax": false, "filename": "ChannelLocation.php", "line": "?"}}, "App\\Models\\Product\\Product": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Product\\Brand": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Product\\Category": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\Channel\\Channel": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product\\Inventory": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FInventory.php&line=1", "ajax": false, "filename": "Inventory.php", "line": "?"}}, "App\\Models\\Channel\\ChannelProduct": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannelProduct.php&line=1", "ajax": false, "filename": "ChannelProduct.php", "line": "?"}}, "App\\Models\\Channel\\ChannelProductStatus": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannelProductStatus.php&line=1", "ajax": false, "filename": "ChannelProductStatus.php", "line": "?"}}, "App\\Models\\Invite\\Invite": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=1", "ajax": false, "filename": "Invite.php", "line": "?"}}}, "count": 95, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/product/1/versions/1/variants\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:2 [\n    0 => \"success\"\n    1 => \"sync_popup\"\n  ]\n]", "password_hash_web": "null", "organization_id": "1", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:49 [\n          \"Handle\" => \"Handle\"\n          \"Title\" => \"Title\"\n          \"Body (HTML)\" => \"Body (HTML)\"\n          \"Vendor\" => \"Vendor\"\n          \"Type\" => \"Type\"\n          \"Tags\" => \"Tags\"\n          \"Published\" => \"Published\"\n          \"Option1 Name\" => \"Option1 Name\"\n          \"Option1 Value\" => \"Option1 Value\"\n          \"Option2 Name\" => \"Option2 Name\"\n          \"Option2 Value\" => \"Option2 Value\"\n          \"Option3 Name\" => \"Option3 Name\"\n          \"Option3 Value\" => \"Option3 Value\"\n          \"Variant SKU\" => \"Variant SKU\"\n          \"Variant Grams\" => \"Variant Grams\"\n          \"Variant Inventory Tracker\" => \"Variant Inventory Tracker\"\n          \"Variant Inventory Qty\" => \"Variant Inventory Qty\"\n          \"Variant Inventory Policy\" => \"Variant Inventory Policy\"\n          \"Variant Fulfillment Service\" => \"Variant Fulfillment Service\"\n          \"Variant Price\" => \"Variant Price\"\n          \"Variant Compare At Price\" => \"Variant Compare At Price\"\n          \"Variant Requires Shipping\" => \"Variant Requires Shipping\"\n          \"Variant Taxable\" => \"Variant Taxable\"\n          \"Variant Barcode\" => \"Variant Barcode\"\n          \"Image Src\" => \"Image Src\"\n          \"Image Position\" => \"Image Position\"\n          \"Image Alt Text\" => \"Image Alt Text\"\n          \"Gift Card\" => \"Gift Card\"\n          \"SEO Title\" => \"SEO Title\"\n          \"SEO Description\" => \"SEO Description\"\n          \"Google Shopping / Google Product Category\" => \"Google Shopping / Google Product Category\"\n          \"Google Shopping / Gender\" => \"Google Shopping / Gender\"\n          \"Google Shopping / Age Group\" => \"Google Shopping / Age Group\"\n          \"Google Shopping / MPN\" => \"Google Shopping / MPN\"\n          \"Google Shopping / AdWords Grouping\" => \"Google Shopping / AdWords Grouping\"\n          \"Google Shopping / AdWords Labels\" => \"Google Shopping / AdWords Labels\"\n          \"Google Shopping / Condition\" => \"Google Shopping / Condition\"\n          \"Google Shopping / Custom Product\" => \"Google Shopping / Custom Product\"\n          \"Google Shopping / Custom Label 0\" => \"Google Shopping / Custom Label 0\"\n          \"Google Shopping / Custom Label 1\" => \"Google Shopping / Custom Label 1\"\n          \"Google Shopping / Custom Label 2\" => \"Google Shopping / Custom Label 2\"\n          \"Google Shopping / Custom Label 3\" => \"Google Shopping / Custom Label 3\"\n          \"Google Shopping / Custom Label 4\" => \"Google Shopping / Custom Label 4\"\n          \"Variant Image\" => \"Variant Image\"\n          \"Variant Weight Unit\" => \"Variant Weight Unit\"\n          \"Variant Tax Code\" => \"Variant Tax Code\"\n          \"Cost per item\" => \"Cost per item\"\n          \"Status\" => \"Status\"\n          \"List\" => \"List\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:49 [\n        0 => array:2 [\n          \"label\" => \"Handle\"\n          \"value\" => \"Default,Handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Title\"\n          \"value\" => \"Default,Title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Body (HTML)\"\n          \"value\" => \"Default,Body (HTML)\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,Vendor\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Type\"\n          \"value\" => \"Default,Type\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"Default,Tags\"\n        ]\n        6 => array:2 [\n          \"label\" => \"Published\"\n          \"value\" => \"Default,Published\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Option1 Name\"\n          \"value\" => \"Default,Option1 Name\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Option1 Value\"\n          \"value\" => \"Default,Option1 Value\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Option2 Name\"\n          \"value\" => \"Default,Option2 Name\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Option2 Value\"\n          \"value\" => \"Default,Option2 Value\"\n        ]\n        11 => array:2 [\n          \"label\" => \"Option3 Name\"\n          \"value\" => \"Default,Option3 Name\"\n        ]\n        12 => array:2 [\n          \"label\" => \"Option3 Value\"\n          \"value\" => \"Default,Option3 Value\"\n        ]\n        13 => array:2 [\n          \"label\" => \"Variant SKU\"\n          \"value\" => \"Default,Variant SKU\"\n        ]\n        14 => array:2 [\n          \"label\" => \"Variant Grams\"\n          \"value\" => \"Default,Variant Grams\"\n        ]\n        15 => array:2 [\n          \"label\" => \"Variant Inventory Tracker\"\n          \"value\" => \"Default,Variant Inventory Tracker\"\n        ]\n        16 => array:2 [\n          \"label\" => \"Variant Inventory Qty\"\n          \"value\" => \"Default,Variant Inventory Qty\"\n        ]\n        17 => array:2 [\n          \"label\" => \"Variant Inventory Policy\"\n          \"value\" => \"Default,Variant Inventory Policy\"\n        ]\n        18 => array:2 [\n          \"label\" => \"Variant Fulfillment Service\"\n          \"value\" => \"Default,Variant Fulfillment Service\"\n        ]\n        19 => array:2 [\n          \"label\" => \"Variant Price\"\n          \"value\" => \"Default,Variant Price\"\n        ]\n        20 => array:2 [\n          \"label\" => \"Variant Compare At Price\"\n          \"value\" => \"Default,Variant Compare At Price\"\n        ]\n        21 => array:2 [\n          \"label\" => \"Variant Requires Shipping\"\n          \"value\" => \"Default,Variant Requires Shipping\"\n        ]\n        22 => array:2 [\n          \"label\" => \"Variant Taxable\"\n          \"value\" => \"Default,Variant Taxable\"\n        ]\n        23 => array:2 [\n          \"label\" => \"Variant Barcode\"\n          \"value\" => \"Default,Variant Barcode\"\n        ]\n        24 => array:2 [\n          \"label\" => \"Image Src\"\n          \"value\" => \"Default,Image Src\"\n        ]\n        25 => array:2 [\n          \"label\" => \"Image Position\"\n          \"value\" => \"Default,Image Position\"\n        ]\n        26 => array:2 [\n          \"label\" => \"Image Alt Text\"\n          \"value\" => \"Default,Image Alt Text\"\n        ]\n        27 => array:2 [\n          \"label\" => \"Gift Card\"\n          \"value\" => \"Default,Gift Card\"\n        ]\n        28 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"Default,SEO Title\"\n        ]\n        29 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"Default,SEO Description\"\n        ]\n        30 => array:2 [\n          \"label\" => \"Google Shopping / Google Product Category\"\n          \"value\" => \"Default,Google Shopping / Google Product Category\"\n        ]\n        31 => array:2 [\n          \"label\" => \"Google Shopping / Gender\"\n          \"value\" => \"Default,Google Shopping / Gender\"\n        ]\n        32 => array:2 [\n          \"label\" => \"Google Shopping / Age Group\"\n          \"value\" => \"Default,Google Shopping / Age Group\"\n        ]\n        33 => array:2 [\n          \"label\" => \"Google Shopping / MPN\"\n          \"value\" => \"Default,Google Shopping / MPN\"\n        ]\n        34 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Grouping\"\n          \"value\" => \"Default,Google Shopping / AdWords Grouping\"\n        ]\n        35 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Labels\"\n          \"value\" => \"Default,Google Shopping / AdWords Labels\"\n        ]\n        36 => array:2 [\n          \"label\" => \"Google Shopping / Condition\"\n          \"value\" => \"Default,Google Shopping / Condition\"\n        ]\n        37 => array:2 [\n          \"label\" => \"Google Shopping / Custom Product\"\n          \"value\" => \"Default,Google Shopping / Custom Product\"\n        ]\n        38 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 0\"\n          \"value\" => \"Default,Google Shopping / Custom Label 0\"\n        ]\n        39 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 1\"\n          \"value\" => \"Default,Google Shopping / Custom Label 1\"\n        ]\n        40 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 2\"\n          \"value\" => \"Default,Google Shopping / Custom Label 2\"\n        ]\n        41 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 3\"\n          \"value\" => \"Default,Google Shopping / Custom Label 3\"\n        ]\n        42 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 4\"\n          \"value\" => \"Default,Google Shopping / Custom Label 4\"\n        ]\n        43 => array:2 [\n          \"label\" => \"Variant Image\"\n          \"value\" => \"Default,Variant Image\"\n        ]\n        44 => array:2 [\n          \"label\" => \"Variant Weight Unit\"\n          \"value\" => \"Default,Variant Weight Unit\"\n        ]\n        45 => array:2 [\n          \"label\" => \"Variant Tax Code\"\n          \"value\" => \"Default,Variant Tax Code\"\n        ]\n        46 => array:2 [\n          \"label\" => \"Cost per item\"\n          \"value\" => \"Default,Cost per item\"\n        ]\n        47 => array:2 [\n          \"label\" => \"Status\"\n          \"value\" => \"Default,Status\"\n        ]\n        48 => array:2 [\n          \"label\" => \"List\"\n          \"value\" => \"Default,List\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"data_required\" => array:8 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"1\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n  ]\n  \"import_action\" => \"3\"\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,1\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:49 [\n    0 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n    ]\n    1 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    2 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    3 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n    ]\n    4 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    5 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n    ]\n    6 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    7 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    8 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    9 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    10 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    11 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    12 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    13 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    14 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    15 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    16 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    17 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    18 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    19 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    20 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    21 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    22 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    23 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    24 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    25 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    26 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    27 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    28 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n    ]\n    29 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n    ]\n    30 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    31 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    32 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    33 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    34 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    35 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    36 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    37 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    38 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    39 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    40 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 2\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    41 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 3\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    42 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 4\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    43 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Image\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    44 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Weight Unit\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    45 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Tax Code\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    46 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Cost per item\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    47 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Status\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    48 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,List\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n  ]\n]", "bulk_edit_data": "array:3 [\n  \"target_page\" => 1\n  \"version_id\" => \"1\"\n  \"productIds\" => array:1 [\n    0 => \"1\"\n  ]\n]", "success": "Product updated successfully", "sync_popup": "true", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/products/1", "action_name": "products.update", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@update", "uri": "PUT products/{product}", "controller": "App\\Http\\Controllers\\Product\\ProductController@update<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=613\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=613\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:613-632</a>", "middleware": "web, check_session", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4cdf3b-f765-4522-844c-d9b9b0c33e35\" target=\"_blank\">View in Telescope</a>", "duration": "1.27s", "peak_memory": "38MB", "response": "Redirect to http://localhost:8000/products/1/edit/1", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-128001428 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-128001428\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1300026296 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>_method</span>\" => \"<span class=sf-dump-str title=\"5 characters\">patch</span>\"\n  \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"15 characters\">testing-product</span>\"\n  \"<span class=sf-dump-key>version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>attribute</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"15 characters\">Testing Product</span>\"\n    <span class=sf-dump-key>2</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>variant_sku</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>barcode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>weight_unit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">kg</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>weight</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>price</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>compare_at_price</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>cost_price</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>variants</span>\" => \"<span class=sf-dump-str title=\"391 characters\">[{&quot;id&quot;:1,&quot;product_id&quot;:1,&quot;version_id&quot;:1,&quot;file_id&quot;:null,&quot;option&quot;:&quot;{\\&quot;attributes\\&quot;:[{\\&quot;id\\&quot;:\\&quot;2\\&quot;}],\\&quot;options\\&quot;:[\\&quot;Default Title\\&quot;]}&quot;,&quot;name&quot;:&quot;Title&quot;,&quot;sku&quot;:null,&quot;price&quot;:null,&quot;compare_at_price&quot;:null,&quot;cost_price&quot;:null,&quot;quantity&quot;:null,&quot;barcode&quot;:null,&quot;weight&quot;:null,&quot;weight_unit&quot;:null,&quot;created_at&quot;:&quot;2025-07-02T07:49:49.000000Z&quot;,&quot;updated_at&quot;:&quot;2025-07-02T07:49:49.000000Z&quot;,&quot;response&quot;:null,&quot;file&quot;:null}]</span>\"\n  \"<span class=sf-dump-key>brand</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>2</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>vendor</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>category</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>channels</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1300026296\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-318737338 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1054</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/x-www-form-urlencoded</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/products/1/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImY0aFZwK1kyZGlRcHNrY1NtN3pQVWc9PSIsInZhbHVlIjoiWGpQVFhJOVg4azdzaWdwLzlKbXVJZ0c1TGVWU3hrWTQrZWpjbkZoK0w5VE8zMmFVQ0tpYjBoeHNOMzRSVE1YZGNLZWg5N1FEZ0ZGdDI1ZUF5TjFMWEFYMlU0ejlFYWpZc2xtRTdDWllGZlVpMHRnd3dLN1RrM3AxR1oyR01sc04iLCJtYWMiOiJmYWIyMzYzOTZjZmY2NTZmMzNmMjYzYjE2M2E4MGZkZTg3MGVkMTFmZDNlZTAzZWUyYTQ3ZjI1NGVlY2U3ZDNiIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjNaZ2VSa3BrZmZxaUMvRjlkWGxBS0E9PSIsInZhbHVlIjoiQ0tPdWtQWEEzWW5ZZnBVSkZGUzdCcWRTRUZXTjFlelgyNVhXS0YwMmFCaEhKS1NSb3YwbCtXUFk5YUY3Y1FxSEdKaW16NWFVMEt0ajNRMGMxWEZVOVRmWWRrMmlLK0w0VHhzOWtLN0tsWFVjeUV0UHR6ODlCRzlNMDYzQWpHSUMiLCJtYWMiOiIzNzQ1YWRiMjFmZmFkM2RjNDM2NTIzMjJhNGFiNjRhOTEzNjc5ZmRiZDg1MDU5MGEzMjVmNTg3Njc3NGVjN2U2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-318737338\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1644000280 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hpj02iYobWSFRbp0Zjz10UyQzMp42LbVgb86a16Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1644000280\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-748427788 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:45:15 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"39 characters\">http://localhost:8000/products/1/edit/1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-748427788\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-499604597 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"55 characters\">http://localhost:8000/api/product/1/versions/1/variants</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"10 characters\">sync_popup</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Handle</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>Body (HTML)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>Vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>Published</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>Option1 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>Option1 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>Option2 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>Option2 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>Option3 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>Option3 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>Variant SKU</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>Variant Grams</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Tracker</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Qty</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>Variant Fulfillment Service</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>Variant Price</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>Variant Compare At Price</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>Variant Requires Shipping</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>Variant Taxable</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>Variant Barcode</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>Image Src</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>Image Position</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>Image Alt Text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>Gift Card</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>SEO Title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>SEO Description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Google Product Category</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Gender</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Age Group</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>Google Shopping / MPN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Grouping</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Labels</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Condition</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Product</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 0</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 1</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 2</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 3</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 4</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>Variant Image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>Variant Weight Unit</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>Variant Tax Code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>Cost per item</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n          </samp>]\n          <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n          </samp>]\n          <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n          </samp>]\n          <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n          </samp>]\n          <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n          </samp>]\n          <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n          </samp>]\n          <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n          </samp>]\n          <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n          </samp>]\n          <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n          </samp>]\n          <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n          </samp>]\n          <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n          </samp>]\n          <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n          </samp>]\n          <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n          </samp>]\n          <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n          </samp>]\n          <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n          </samp>]\n          <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n          </samp>]\n          <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n          </samp>]\n          <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n          </samp>]\n          <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n          </samp>]\n          <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n          </samp>]\n          <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n          </samp>]\n          <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n          </samp>]\n          <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n          </samp>]\n          <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n          </samp>]\n          <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>bulk_edit_data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>target_page</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>productIds</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Product updated successfully</span>\"\n  \"<span class=sf-dump-key>sync_popup</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-499604597\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/products/1", "action_name": "products.update", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@update"}, "badge": "302 Found"}}