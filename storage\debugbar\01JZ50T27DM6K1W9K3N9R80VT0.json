{"__meta": {"id": "01JZ50T27DM6K1W9K3N9R80VT0", "datetime": "2025-07-02 07:32:21", "utime": **********.358972, "method": "GET", "uri": "/api/2024-12/shopify-sync-status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:32:21] LOG.warning: Optional parameter $id declared before required parameter $version_id is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Channel\\ShopifyChannel.php on line 716", "message_html": null, "is_string": false, "label": "warning", "time": **********.26351, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.288816, "end": **********.359002, "duration": 1.0701861381530762, "duration_str": "1.07s", "measures": [{"label": "Booting", "start": **********.288816, "relative_start": 0, "end": **********.819744, "relative_end": **********.819744, "duration": 0.****************, "duration_str": "531ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.819762, "relative_start": 0.****************, "end": **********.359005, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "539ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.840952, "relative_start": 0.****************, "end": **********.845335, "relative_end": **********.845335, "duration": 0.004383087158203125, "duration_str": "4.38ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.351401, "relative_start": 1.****************, "end": **********.351907, "relative_end": **********.351907, "duration": 0.0005059242248535156, "duration_str": "506μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.354456, "relative_start": 1.****************, "end": **********.35457, "relative_end": **********.35457, "duration": 0.00011396408081054688, "duration_str": "114μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/shopify-sync-status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:185-222</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00599, "accumulated_duration_str": "5.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.873616, "duration": 0.0030099999999999997, "duration_str": "3.01ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 50.25}, {"sql": "select * from `channels` where `organization_id` is null and exists (select * from `shopify_channels` where `channels`.`id` = `shopify_channels`.`channel_id`)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 189}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.269012, "duration": 0.00298, "duration_str": "2.98ms", "memory": 0, "memory_str": null, "filename": "DashboardController.php:189", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=189", "ajax": false, "filename": "DashboardController.php", "line": "189"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.25, "width_percent": 49.75}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "27K9BQkxBeypYSnYlTYXl28C9XVvij8Z3ei58vwt", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/shopify-sync-status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus", "uri": "GET api/2024-12/shopify-sync-status", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:185-222</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4aed17-85ce-442b-b3de-b635e4f4d7be\" target=\"_blank\">View in Telescope</a>", "duration": "1.07s", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1494581439 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1494581439\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-9302609 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-9302609\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-959071441 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5zeG45eU9zTGUrZldVbDZicngxelE9PSIsInZhbHVlIjoiMHlWNjE4Snhzb0taMTIrUzF2RVlsdHM3elJNY3VNQUNPblcrcExZeXozWUVvL21IOXZTWUg0V3RhVXhNeWlrNnhUOEFib1pZTTJicTlBRm9udmVucVc3aGFDU3J3RS9VeTNNVzY1cHRVVzhPRlNGVnhKUTFCYnR2TGhvZUM4QmciLCJtYWMiOiJkMzgwMjAyNDgzNGU2NzgyYjI0ZmQxMjYwZjdkMmZkMmFkMzQ3ZDU5MmY1NzE3YTc0YzUwYTU0NmI3MDRiNjUyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjdFdXdXUmp5bDdNT1RtbCtOejRlaEE9PSIsInZhbHVlIjoibzR1b2FKdExReXR2ZkdqYUpKeXBLNVVSL3B1NHdmM0N1U1MvSlFJTjhHaHFQRWg1UjUzSURDcXVDZ1p3blhRRHFyWW9MTndlUlNEbWs0SmMrUWFrWTFSZisrRHZaaERoSGQrWmFHWjlGV2JFNFl1RWhVeDR2MnpJS2NaUXd6QUVYUEtuVUpUQmdXWTl1OHF5NnVVaWt3PT0iLCJtYWMiOiI0ZTRjMzM1NDliYjEyMGIzNmVmMTUzMjFhMjQ0M2QzM2U0NWZmNWFkZTVmYzVkMzY4MGQxNTQwMGFlZTM5MWRiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5zeG45eU9zTGUrZldVbDZicngxelE9PSIsInZhbHVlIjoiMHlWNjE4Snhzb0taMTIrUzF2RVlsdHM3elJNY3VNQUNPblcrcExZeXozWUVvL21IOXZTWUg0V3RhVXhNeWlrNnhUOEFib1pZTTJicTlBRm9udmVucVc3aGFDU3J3RS9VeTNNVzY1cHRVVzhPRlNGVnhKUTFCYnR2TGhvZUM4QmciLCJtYWMiOiJkMzgwMjAyNDgzNGU2NzgyYjI0ZmQxMjYwZjdkMmZkMmFkMzQ3ZDU5MmY1NzE3YTc0YzUwYTU0NmI3MDRiNjUyIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjMwdjFuSCt2UkQzNTJ3RWtuOUQyYUE9PSIsInZhbHVlIjoiYWl4Z1MzbFhiK3VRdXROWGpxZFMwNFl5RXZtUUF0NjV6enAvZ1BqYTlNUENqRWZLU2h1UWNZMWg5aTdQWkJOSHFyWkR1S2FYbHdrcDRRSnpDUnFPNDJ3ckw5dnlWa2lVY3JWMk5NbmdjekVraUwxckljeWUyRmh0UE9JVy9iWDMiLCJtYWMiOiI5ZDgxNDJkMDhjZWFlYTc5ZjIyZjczYThjNWVkZjA3Mzg2NjAxZTBiMjE0MGRkNDkzZjBiNzI2NGRkMTkyNmY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959071441\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1515591537 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|4gSMW5C3jpJL06LZ3KwxPstmT881TikM3cfXR2CuxNxGXZi504fXE5SD9EZN|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27K9BQkxBeypYSnYlTYXl28C9XVvij8Z3ei58vwt</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gt9t9h9opwJ0eGblfTsIhnfaGUc9n3TcmvQB6EdO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515591537\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1697257754 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:32:21 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">57</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik4zSjZiSUIwcFdOOWk0VFFqM2ZXMGc9PSIsInZhbHVlIjoieTBFZDBCSDR3NTlISFgxcEIxZmprRjhRcE5kM0k4SXpBYnFTL3lCdDd4MUpKTmtQbStRSEdaOEVHN1lEWmxHbEVpaitrOHFQdVpZalE0THllbGhRblVKdzJvbmxpY2Fpc1Buamc2cERCS1ZHTzZBbEIyK1ZyWG1zQ1BBVHc3TWQiLCJtYWMiOiI0NDQ5YjExODk3MDBlNDQzY2Q3NDRlZWZkZjI2ZDhhMTlhNDMwZTZhZGE0NmI4ZGE5Y2M4YzU4NzAyYjI1MzcxIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:32:21 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6IlJ2QldmcWo0UDNJZFRqbC9oRzdWdXc9PSIsInZhbHVlIjoiZU5WN3V4N0dkQ2lZUzBBN2xKbGg5RndFb1JPU2FhOFJtczVmUzQ3Q3VZeEJpYUxHTlBNaHlZREduL0hySmpvNlAwdm92Sk8rSDl0bVRIVXdMaDhVM0lDcmhnMmJZaXhvUTM0U3Y3RXM1VHlDU2RRTUxjS25FVTdCZW95Tng4N3UiLCJtYWMiOiJlNDIxYjA5ODAxMzFlMzdhYThlMWNiYzBkNmU1MzljYjIyNGFmNjgyZTc1OTI0MmFjMDFkYmM4M2VlNDBlMzQ3IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:32:21 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik4zSjZiSUIwcFdOOWk0VFFqM2ZXMGc9PSIsInZhbHVlIjoieTBFZDBCSDR3NTlISFgxcEIxZmprRjhRcE5kM0k4SXpBYnFTL3lCdDd4MUpKTmtQbStRSEdaOEVHN1lEWmxHbEVpaitrOHFQdVpZalE0THllbGhRblVKdzJvbmxpY2Fpc1Buamc2cERCS1ZHTzZBbEIyK1ZyWG1zQ1BBVHc3TWQiLCJtYWMiOiI0NDQ5YjExODk3MDBlNDQzY2Q3NDRlZWZkZjI2ZDhhMTlhNDMwZTZhZGE0NmI4ZGE5Y2M4YzU4NzAyYjI1MzcxIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:32:21 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6IlJ2QldmcWo0UDNJZFRqbC9oRzdWdXc9PSIsInZhbHVlIjoiZU5WN3V4N0dkQ2lZUzBBN2xKbGg5RndFb1JPU2FhOFJtczVmUzQ3Q3VZeEJpYUxHTlBNaHlZREduL0hySmpvNlAwdm92Sk8rSDl0bVRIVXdMaDhVM0lDcmhnMmJZaXhvUTM0U3Y3RXM1VHlDU2RRTUxjS25FVTdCZW95Tng4N3UiLCJtYWMiOiJlNDIxYjA5ODAxMzFlMzdhYThlMWNiYzBkNmU1MzljYjIyNGFmNjgyZTc1OTI0MmFjMDFkYmM4M2VlNDBlMzQ3IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:32:21 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1697257754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1703318616 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27K9BQkxBeypYSnYlTYXl28C9XVvij8Z3ei58vwt</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/shopify-sync-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1703318616\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus"}, "badge": null}}