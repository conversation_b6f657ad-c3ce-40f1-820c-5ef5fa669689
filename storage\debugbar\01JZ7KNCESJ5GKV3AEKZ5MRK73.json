{"__meta": {"id": "01JZ7KNCESJ5GKV3AEKZ5MRK73", "datetime": "2025-07-03 07:40:19", "utime": **********.801977, "method": "GET", "uri": "/products", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751528418.901169, "end": **********.801996, "duration": 0.9008269309997559, "duration_str": "901ms", "measures": [{"label": "Booting", "start": 1751528418.901169, "relative_start": 0, "end": **********.425359, "relative_end": **********.425359, "duration": 0.****************, "duration_str": "524ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.425373, "relative_start": 0.****************, "end": **********.801998, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "377ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.440443, "relative_start": 0.****************, "end": **********.44885, "relative_end": **********.44885, "duration": 0.008406877517700195, "duration_str": "8.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.544594, "relative_start": 0.****************, "end": **********.799562, "relative_end": **********.799562, "duration": 0.*****************, "duration_str": "255ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x products.index", "param_count": null, "params": [], "start": **********.547227, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/index.blade.phpproducts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fproducts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "products.index"}, {"name": "1x components.products.add-product", "param_count": null, "params": [], "start": **********.59862, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/add-product.blade.phpcomponents.products.add-product", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fadd-product.blade.php&line=1", "ajax": false, "filename": "add-product.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.add-product"}, {"name": "1x layouts.app_new", "param_count": null, "params": [], "start": **********.600997, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.phplayouts.app_new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=1", "ajax": false, "filename": "app_new.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app_new"}, {"name": "1x layouts.navs.product_sidebar", "param_count": null, "params": [], "start": **********.603231, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.phplayouts.navs.product_sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=1", "ajax": false, "filename": "product_sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navs.product_sidebar"}, {"name": "1x components.alerts.import-alert", "param_count": null, "params": [], "start": **********.779322, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/import-alert.blade.phpcomponents.alerts.import-alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fimport-alert.blade.php&line=1", "ajax": false, "filename": "import-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.import-alert"}, {"name": "1x components.alerts.upgrade-billing-modal", "param_count": null, "params": [], "start": **********.789368, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/upgrade-billing-modal.blade.phpcomponents.alerts.upgrade-billing-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fupgrade-billing-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-billing-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.upgrade-billing-modal"}, {"name": "1x components.assets.delete-modal", "param_count": null, "params": [], "start": **********.790448, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/assets/delete-modal.blade.phpcomponents.assets.delete-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fassets%2Fdelete-modal.blade.php&line=1", "ajax": false, "filename": "delete-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.assets.delete-modal"}]}, "route": {"uri": "GET products", "middleware": "web, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ProductController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "as": "products.index", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:51-68</a>"}, "queries": {"count": 58, "nb_statements": 58, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06144, "accumulated_duration_str": "61.44ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.4767952, "duration": 0.01905, "duration_str": "19.05ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 31.006}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.504901, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.006, "width_percent": 1.156}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.521584, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.161, "width_percent": 2.246}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.532195, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.408, "width_percent": 1.074}, {"sql": "select * from `batch_progress` where `user_id` = 1 and `organization_id` = '1' and `status` = 0 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, "1", 0, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.53946, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:60", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=60", "ajax": false, "filename": "ProductController.php", "line": "60"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.482, "width_percent": 1.204}, {"sql": "select `id` from `organization_user` where (`user_id` = 1 and `organization_id` = '1') limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 65}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.54964, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:57", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=57", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.686, "width_percent": 1.221}, {"sql": "select `id` from `permissions` where `handle` = 'add_and_edit_product' limit 1", "type": "query", "params": [], "bindings": ["add_and_edit_product"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.553659, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:61", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=61", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "61"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.907, "width_percent": 0.765}, {"sql": "select * from `organization_user_permissions` where (`organization_user_id` = 1 and `permission_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}], "start": **********.559674, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:71", "source": {"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=71", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "71"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.672, "width_percent": 0.928}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.5636508, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.6, "width_percent": 1.497}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.567435, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.097, "width_percent": 1.139}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.574296, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.236, "width_percent": 1.481}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.5780702, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.717, "width_percent": 1.074}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.583564, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 44.792, "width_percent": 1.432}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.587564, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 46.224, "width_percent": 1.074}, {"sql": "select * from `channels` where `organization_id` = '1' and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/AddProduct.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\AddProduct.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.594997, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "AddProduct.php:20", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/AddProduct.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\AddProduct.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FAddProduct.php&line=20", "ajax": false, "filename": "AddProduct.php", "line": "20"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.298, "width_percent": 1.156}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.605247, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 48.454, "width_percent": 1.546}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.609854, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50, "width_percent": 1.204}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.614022, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 51.204, "width_percent": 1.383}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.617628, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.588, "width_percent": 1.074}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.6229029, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.662, "width_percent": 2.637}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.627182, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.299, "width_percent": 1.123}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.631344, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 57.422, "width_percent": 1.335}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.635387, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.757, "width_percent": 1.253}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.640646, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.01, "width_percent": 1.351}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.644273, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.361, "width_percent": 1.107}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.6485841, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.467, "width_percent": 1.383}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.6526911, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 63.851, "width_percent": 1.123}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.657798, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 64.974, "width_percent": 1.383}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.6613579, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.357, "width_percent": 1.156}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.665702, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 67.513, "width_percent": 1.416}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.670473, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 68.929, "width_percent": 1.058}, {"sql": "select `id` from `organization_user` where (`user_id` = 1 and `organization_id` = '1') limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 65}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.6759942, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:57", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=57", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.987, "width_percent": 1.058}, {"sql": "select `id` from `permissions` where `handle` = 'add_and_edit_product' limit 1", "type": "query", "params": [], "bindings": ["add_and_edit_product"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.6793, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:61", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=61", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "61"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.045, "width_percent": 0.846}, {"sql": "select * from `organization_user_permissions` where (`organization_user_id` = 1 and `permission_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}], "start": **********.683365, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:71", "source": {"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=71", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "71"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.891, "width_percent": 1.025}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.6874251, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.917, "width_percent": 1.367}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.692332, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.284, "width_percent": 0.781}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.696526, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.065, "width_percent": 1.058}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.701157, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.123, "width_percent": 1.09}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.705744, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.214, "width_percent": 1.204}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.709323, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.418, "width_percent": 0.911}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.714317, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.329, "width_percent": 1.123}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.718144, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.452, "width_percent": 0.993}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7228, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.445, "width_percent": 1.253}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.726357, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.699, "width_percent": 0.911}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.731447, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.61, "width_percent": 1.237}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.735241, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.847, "width_percent": 0.977}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.740073, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.824, "width_percent": 1.563}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7441518, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.386, "width_percent": 0.96}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.7497709, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.346, "width_percent": 1.188}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.753237, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.535, "width_percent": 0.977}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.759146, "duration": 0.0009599999999999999, "duration_str": "960μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.511, "width_percent": 1.563}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.762877, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.074, "width_percent": 0.879}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.766919, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.952, "width_percent": 1.188}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.770439, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.141, "width_percent": 0.879}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.7749681, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "layouts.navs.product_sidebar:435", "source": {"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=435", "ajax": false, "filename": "product_sidebar.blade.php", "line": "435"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.02, "width_percent": 1.676}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, "1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.780757, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:249", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=249", "ajax": false, "filename": "app_new.blade.php", "line": "249"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.696, "width_percent": 1.351}, {"sql": "select * from `versions` where `versions`.`organization_id` = 1 and `versions`.`organization_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.7855198, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:251", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=251", "ajax": false, "filename": "app_new.blade.php", "line": "251"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.047, "width_percent": 0.944}, {"sql": "select count(*) as aggregate from `products` where `organization_id` = '1' and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, {"index": 17, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 360}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.796014, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "User.php:193", "source": {"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=193", "ajax": false, "filename": "User.php", "line": "193"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.991, "width_percent": 1.009}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUserPermission": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUserPermission.php&line=1", "ajax": false, "filename": "OrganizationUserPermission.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Channel\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}}, "count": 33, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 24, "messages": [{"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1450260537 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1450260537\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.537456, "xdebug_link": null}, {"message": "[\n  ability => add_and_edit_product,\n  target => App\\Models\\Organization\\OrganizationUserPermission,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Organization\\OrganizationUserPermission, 1 => 1]\n]", "message_html": "<pre class=sf-dump id=sf-dump-28383582 data-indent-pad=\"  \"><span class=sf-dump-note>add_and_edit_product App\\Models\\Organization\\OrganizationUserPermission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">add_and_edit_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"50 characters\">App\\Models\\Organization\\OrganizationUserPermission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"65 characters\">[0 =&gt; App\\Models\\Organization\\OrganizationUserPermission, 1 =&gt; 1]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-28383582\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.562769, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-85682631 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-85682631\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.570755, "xdebug_link": null}, {"message": "[\n  ability => create-product,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => [0 => Object(Illuminate\\Database\\Eloquent\\Builder)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-971227994 data-indent-pad=\"  \"><span class=sf-dump-note>create-product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create-product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"51 characters\">[0 =&gt; Object(Illuminate\\Database\\Eloquent\\Builder)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-971227994\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.581397, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => import,\n  result => true,\n  user => 1,\n  arguments => [0 => import]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1770175108 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess import</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; import]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1770175108\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.591471, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2000891470 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2000891470\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.613161, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1985503046 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1985503046\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.621629, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-696660916 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-696660916\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.630502, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => invite-team,\n  result => true,\n  user => 1,\n  arguments => [0 => invite-team]\n]", "message_html": "<pre class=sf-dump id=sf-dump-273877344 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess invite-team</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"11 characters\">invite-team</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"18 characters\">[0 =&gt; invite-team]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-273877344\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.639472, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => notification,\n  result => true,\n  user => 1,\n  arguments => [0 => notification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-238035403 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess notification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"12 characters\">notification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"19 characters\">[0 =&gt; notification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-238035403\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.647629, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => settings,\n  result => true,\n  user => 1,\n  arguments => [0 => settings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-284193781 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; settings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-284193781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.65685, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1138749437 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1138749437\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.664626, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => import,\n  result => true,\n  user => 1,\n  arguments => [0 => import]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1323039634 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess import</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; import]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323039634\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.675469, "xdebug_link": null}, {"message": "[\n  ability => add_and_edit_product,\n  target => App\\Models\\Organization\\OrganizationUserPermission,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Organization\\OrganizationUserPermission, 1 => 1]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1696869647 data-indent-pad=\"  \"><span class=sf-dump-note>add_and_edit_product App\\Models\\Organization\\OrganizationUserPermission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">add_and_edit_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"50 characters\">App\\Models\\Organization\\OrganizationUserPermission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"65 characters\">[0 =&gt; App\\Models\\Organization\\OrganizationUserPermission, 1 =&gt; 1]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1696869647\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.686532, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => export,\n  result => true,\n  user => 1,\n  arguments => [0 => export]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1143827128 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess export</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; export]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1143827128\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.695448, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => category,\n  result => true,\n  user => 1,\n  arguments => [0 => category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-11549947 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11549947\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.704478, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute-set,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute-set]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1761395473 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute-set</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"13 characters\">attribute-set</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[0 =&gt; attribute-set]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761395473\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.713066, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute]\n]", "message_html": "<pre class=sf-dump id=sf-dump-121222188 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"9 characters\">attribute</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[0 =&gt; attribute]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-121222188\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.721439, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => variant-option,\n  result => true,\n  user => 1,\n  arguments => [0 => variant-option]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1383262732 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess variant-option</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"14 characters\">variant-option</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"21 characters\">[0 =&gt; variant-option]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1383262732\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.730371, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => brand,\n  result => true,\n  user => 1,\n  arguments => [0 => brand]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1180796597 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess brand</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">brand</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; brand]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180796597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.738586, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => vendor,\n  result => true,\n  user => 1,\n  arguments => [0 => vendor]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1792410550 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess vendor</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">vendor</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; vendor]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1792410550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.74741, "xdebug_link": null}, {"message": "[\n  ability => create-lang,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => [0 => Object(Illuminate\\Database\\Eloquent\\Builder)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-441617075 data-indent-pad=\"  \"><span class=sf-dump-note>create-lang </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-lang</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"51 characters\">[0 =&gt; Object(Illuminate\\Database\\Eloquent\\Builder)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-441617075\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.757972, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => location,\n  result => true,\n  user => 1,\n  arguments => [0 => location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-18790231 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-18790231\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.766015, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => store,\n  result => true,\n  user => 1,\n  arguments => [0 => store]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1035279957 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess store</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; store]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1035279957\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.774121, "xdebug_link": null}]}, "session": {"_token": "9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/image-quality-score\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "organization_id": "1", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:49 [\n          \"Handle\" => \"Handle\"\n          \"Title\" => \"Title\"\n          \"Body (HTML)\" => \"Body (HTML)\"\n          \"Vendor\" => \"Vendor\"\n          \"Type\" => \"Type\"\n          \"Tags\" => \"Tags\"\n          \"Published\" => \"Published\"\n          \"Option1 Name\" => \"Option1 Name\"\n          \"Option1 Value\" => \"Option1 Value\"\n          \"Option2 Name\" => \"Option2 Name\"\n          \"Option2 Value\" => \"Option2 Value\"\n          \"Option3 Name\" => \"Option3 Name\"\n          \"Option3 Value\" => \"Option3 Value\"\n          \"Variant SKU\" => \"Variant SKU\"\n          \"Variant Grams\" => \"Variant Grams\"\n          \"Variant Inventory Tracker\" => \"Variant Inventory Tracker\"\n          \"Variant Inventory Qty\" => \"Variant Inventory Qty\"\n          \"Variant Inventory Policy\" => \"Variant Inventory Policy\"\n          \"Variant Fulfillment Service\" => \"Variant Fulfillment Service\"\n          \"Variant Price\" => \"Variant Price\"\n          \"Variant Compare At Price\" => \"Variant Compare At Price\"\n          \"Variant Requires Shipping\" => \"Variant Requires Shipping\"\n          \"Variant Taxable\" => \"Variant Taxable\"\n          \"Variant Barcode\" => \"Variant Barcode\"\n          \"Image Src\" => \"Image Src\"\n          \"Image Position\" => \"Image Position\"\n          \"Image Alt Text\" => \"Image Alt Text\"\n          \"Gift Card\" => \"Gift Card\"\n          \"SEO Title\" => \"SEO Title\"\n          \"SEO Description\" => \"SEO Description\"\n          \"Google Shopping / Google Product Category\" => \"Google Shopping / Google Product Category\"\n          \"Google Shopping / Gender\" => \"Google Shopping / Gender\"\n          \"Google Shopping / Age Group\" => \"Google Shopping / Age Group\"\n          \"Google Shopping / MPN\" => \"Google Shopping / MPN\"\n          \"Google Shopping / AdWords Grouping\" => \"Google Shopping / AdWords Grouping\"\n          \"Google Shopping / AdWords Labels\" => \"Google Shopping / AdWords Labels\"\n          \"Google Shopping / Condition\" => \"Google Shopping / Condition\"\n          \"Google Shopping / Custom Product\" => \"Google Shopping / Custom Product\"\n          \"Google Shopping / Custom Label 0\" => \"Google Shopping / Custom Label 0\"\n          \"Google Shopping / Custom Label 1\" => \"Google Shopping / Custom Label 1\"\n          \"Google Shopping / Custom Label 2\" => \"Google Shopping / Custom Label 2\"\n          \"Google Shopping / Custom Label 3\" => \"Google Shopping / Custom Label 3\"\n          \"Google Shopping / Custom Label 4\" => \"Google Shopping / Custom Label 4\"\n          \"Variant Image\" => \"Variant Image\"\n          \"Variant Weight Unit\" => \"Variant Weight Unit\"\n          \"Variant Tax Code\" => \"Variant Tax Code\"\n          \"Cost per item\" => \"Cost per item\"\n          \"Status\" => \"Status\"\n          \"List\" => \"List\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:49 [\n        0 => array:2 [\n          \"label\" => \"Handle\"\n          \"value\" => \"Default,Handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Title\"\n          \"value\" => \"Default,Title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Body (HTML)\"\n          \"value\" => \"Default,Body (HTML)\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,Vendor\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Type\"\n          \"value\" => \"Default,Type\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"Default,Tags\"\n        ]\n        6 => array:2 [\n          \"label\" => \"Published\"\n          \"value\" => \"Default,Published\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Option1 Name\"\n          \"value\" => \"Default,Option1 Name\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Option1 Value\"\n          \"value\" => \"Default,Option1 Value\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Option2 Name\"\n          \"value\" => \"Default,Option2 Name\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Option2 Value\"\n          \"value\" => \"Default,Option2 Value\"\n        ]\n        11 => array:2 [\n          \"label\" => \"Option3 Name\"\n          \"value\" => \"Default,Option3 Name\"\n        ]\n        12 => array:2 [\n          \"label\" => \"Option3 Value\"\n          \"value\" => \"Default,Option3 Value\"\n        ]\n        13 => array:2 [\n          \"label\" => \"Variant SKU\"\n          \"value\" => \"Default,Variant SKU\"\n        ]\n        14 => array:2 [\n          \"label\" => \"Variant Grams\"\n          \"value\" => \"Default,Variant Grams\"\n        ]\n        15 => array:2 [\n          \"label\" => \"Variant Inventory Tracker\"\n          \"value\" => \"Default,Variant Inventory Tracker\"\n        ]\n        16 => array:2 [\n          \"label\" => \"Variant Inventory Qty\"\n          \"value\" => \"Default,Variant Inventory Qty\"\n        ]\n        17 => array:2 [\n          \"label\" => \"Variant Inventory Policy\"\n          \"value\" => \"Default,Variant Inventory Policy\"\n        ]\n        18 => array:2 [\n          \"label\" => \"Variant Fulfillment Service\"\n          \"value\" => \"Default,Variant Fulfillment Service\"\n        ]\n        19 => array:2 [\n          \"label\" => \"Variant Price\"\n          \"value\" => \"Default,Variant Price\"\n        ]\n        20 => array:2 [\n          \"label\" => \"Variant Compare At Price\"\n          \"value\" => \"Default,Variant Compare At Price\"\n        ]\n        21 => array:2 [\n          \"label\" => \"Variant Requires Shipping\"\n          \"value\" => \"Default,Variant Requires Shipping\"\n        ]\n        22 => array:2 [\n          \"label\" => \"Variant Taxable\"\n          \"value\" => \"Default,Variant Taxable\"\n        ]\n        23 => array:2 [\n          \"label\" => \"Variant Barcode\"\n          \"value\" => \"Default,Variant Barcode\"\n        ]\n        24 => array:2 [\n          \"label\" => \"Image Src\"\n          \"value\" => \"Default,Image Src\"\n        ]\n        25 => array:2 [\n          \"label\" => \"Image Position\"\n          \"value\" => \"Default,Image Position\"\n        ]\n        26 => array:2 [\n          \"label\" => \"Image Alt Text\"\n          \"value\" => \"Default,Image Alt Text\"\n        ]\n        27 => array:2 [\n          \"label\" => \"Gift Card\"\n          \"value\" => \"Default,Gift Card\"\n        ]\n        28 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"Default,SEO Title\"\n        ]\n        29 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"Default,SEO Description\"\n        ]\n        30 => array:2 [\n          \"label\" => \"Google Shopping / Google Product Category\"\n          \"value\" => \"Default,Google Shopping / Google Product Category\"\n        ]\n        31 => array:2 [\n          \"label\" => \"Google Shopping / Gender\"\n          \"value\" => \"Default,Google Shopping / Gender\"\n        ]\n        32 => array:2 [\n          \"label\" => \"Google Shopping / Age Group\"\n          \"value\" => \"Default,Google Shopping / Age Group\"\n        ]\n        33 => array:2 [\n          \"label\" => \"Google Shopping / MPN\"\n          \"value\" => \"Default,Google Shopping / MPN\"\n        ]\n        34 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Grouping\"\n          \"value\" => \"Default,Google Shopping / AdWords Grouping\"\n        ]\n        35 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Labels\"\n          \"value\" => \"Default,Google Shopping / AdWords Labels\"\n        ]\n        36 => array:2 [\n          \"label\" => \"Google Shopping / Condition\"\n          \"value\" => \"Default,Google Shopping / Condition\"\n        ]\n        37 => array:2 [\n          \"label\" => \"Google Shopping / Custom Product\"\n          \"value\" => \"Default,Google Shopping / Custom Product\"\n        ]\n        38 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 0\"\n          \"value\" => \"Default,Google Shopping / Custom Label 0\"\n        ]\n        39 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 1\"\n          \"value\" => \"Default,Google Shopping / Custom Label 1\"\n        ]\n        40 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 2\"\n          \"value\" => \"Default,Google Shopping / Custom Label 2\"\n        ]\n        41 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 3\"\n          \"value\" => \"Default,Google Shopping / Custom Label 3\"\n        ]\n        42 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 4\"\n          \"value\" => \"Default,Google Shopping / Custom Label 4\"\n        ]\n        43 => array:2 [\n          \"label\" => \"Variant Image\"\n          \"value\" => \"Default,Variant Image\"\n        ]\n        44 => array:2 [\n          \"label\" => \"Variant Weight Unit\"\n          \"value\" => \"Default,Variant Weight Unit\"\n        ]\n        45 => array:2 [\n          \"label\" => \"Variant Tax Code\"\n          \"value\" => \"Default,Variant Tax Code\"\n        ]\n        46 => array:2 [\n          \"label\" => \"Cost per item\"\n          \"value\" => \"Default,Cost per item\"\n        ]\n        47 => array:2 [\n          \"label\" => \"Status\"\n          \"value\" => \"Default,Status\"\n        ]\n        48 => array:2 [\n          \"label\" => \"List\"\n          \"value\" => \"Default,List\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"data_required\" => array:8 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"1\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n  ]\n  \"import_action\" => \"3\"\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,1\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:49 [\n    0 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n    ]\n    1 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    2 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    3 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n    ]\n    4 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    5 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n    ]\n    6 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    7 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    8 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    9 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    10 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    11 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    12 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    13 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    14 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    15 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    16 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    17 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    18 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    19 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    20 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    21 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    22 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    23 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    24 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    25 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    26 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    27 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    28 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n    ]\n    29 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n    ]\n    30 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    31 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    32 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    33 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    34 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    35 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    36 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    37 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    38 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    39 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    40 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 2\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    41 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 3\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    42 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 4\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    43 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Image\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    44 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Weight Unit\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    45 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Tax Code\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    46 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Cost per item\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    47 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Status\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    48 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,List\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n  ]\n]", "bulk_edit_data": "array:3 [\n  \"target_page\" => 1\n  \"version_id\" => \"1\"\n  \"productIds\" => array:1 [\n    0 => \"1\"\n  ]\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/products", "action_name": "products.index", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@index", "uri": "GET products", "controller": "App\\Http\\Controllers\\Product\\ProductController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:51-68</a>", "middleware": "web", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4cf2ed-8a46-49db-8327-bf5cc97314e5\" target=\"_blank\">View in Telescope</a>", "duration": "922ms", "peak_memory": "38MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1898852892 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1898852892\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1928696780 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1928696780\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1066385245 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/products?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InJyc0FSOEZhSjA2Y3J5ZEZKYVpibkE9PSIsInZhbHVlIjoiaTJvaTFCbThFUUxENmh2d1JCODV6eHFTdGplL3doS1h5OG5iRFlONG5qV3lhTVBTMERDTkNkSDltYWxVdTdsUnJDeDlaWjViU1l1cDBFYnpTMmQ4Z05LMGZPYnorVm9lc2hCWUJPb2Nzc3lpbHVrY01Zd3dCRTJvSTQrazIxOG8iLCJtYWMiOiIxNGRiYjZjOGQ0NmQ0OTc5NGNkZGZlMWZkZWY5NGUxZjM3ZGMzNzQ2NDJhOTYzNTNhZWMxMDI0YjlkZWZjMzdhIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImtMUE01c1ZXVGFDVGg5dEx2MTFBK2c9PSIsInZhbHVlIjoiTVZHSG5QNHMxK2JWc2ZLcTNtaXhHQUtZeFJBcEUrRWM3YXpRSEkwUTExSlFTMGpjaWJCN1BteE1MRmdrUUNkUVYrNXZ0Nkx3aDFqaFRvK2N3Q2ZPMUljM2dRa1kySlhGWFVPMnVQSjZiVUdEUVd3UktVT21PelloWGxOTTBPcUQiLCJtYWMiOiJlNWMxMDkyOTZhNTE4MDdhMDI5ZTUyNjgzM2UwMWRkMDIyNjMyZmRmOGUyOTQ4NjBkOTVmMGE3NjJiMGQxODZhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1066385245\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1232470627 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hpj02iYobWSFRbp0Zjz10UyQzMp42LbVgb86a16Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1232470627\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-747625962 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 07:40:19 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-747625962\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1077051261 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/image-quality-score</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Handle</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>Body (HTML)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>Vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>Published</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>Option1 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>Option1 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>Option2 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>Option2 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>Option3 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>Option3 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>Variant SKU</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>Variant Grams</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Tracker</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Qty</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>Variant Fulfillment Service</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>Variant Price</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>Variant Compare At Price</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>Variant Requires Shipping</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>Variant Taxable</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>Variant Barcode</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>Image Src</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>Image Position</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>Image Alt Text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>Gift Card</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>SEO Title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>SEO Description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Google Product Category</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Gender</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Age Group</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>Google Shopping / MPN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Grouping</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Labels</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Condition</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Product</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 0</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 1</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 2</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 3</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 4</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>Variant Image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>Variant Weight Unit</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>Variant Tax Code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>Cost per item</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n          </samp>]\n          <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n          </samp>]\n          <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n          </samp>]\n          <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n          </samp>]\n          <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n          </samp>]\n          <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n          </samp>]\n          <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n          </samp>]\n          <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n          </samp>]\n          <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n          </samp>]\n          <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n          </samp>]\n          <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n          </samp>]\n          <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n          </samp>]\n          <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n          </samp>]\n          <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n          </samp>]\n          <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n          </samp>]\n          <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n          </samp>]\n          <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n          </samp>]\n          <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n          </samp>]\n          <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n          </samp>]\n          <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n          </samp>]\n          <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n          </samp>]\n          <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n          </samp>]\n          <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n          </samp>]\n          <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n          </samp>]\n          <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>bulk_edit_data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>target_page</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>productIds</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077051261\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/products", "action_name": "products.index", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@index"}, "badge": null}}