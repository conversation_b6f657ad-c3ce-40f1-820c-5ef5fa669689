<?php ?>

<?php $__env->startSection('titles','Categories'); ?>
<?php $__env->startSection('content'); ?>
    <div>

        <?php if (isset($component)) { $__componentOriginal262b0e1ee858dd683d724746646aea00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal262b0e1ee858dd683d724746646aea00 = $attributes; } ?>
<?php $component = App\View\Components\Products\PageTitle::resolve(['name' => ''.e(trans('products_categories.page_title')).'','description' => ''.e(trans('products_categories.page_description')).'','links' => 'true','button' => 'true'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\PageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('addbutton', null, []); ?> 
                <a href="<?php echo e(route('categories.create')); ?>" id="add-category"
                   class="btn btn-primary float-lg-right float-md-right only-disabled">
                    <?php echo e(trans('products_categories.add_category_btn')); ?>

                </a>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $attributes = $__attributesOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__attributesOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $component = $__componentOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__componentOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>

        <div class="row">
            <div class="col-12 col-md-6 col-xl-3">
                <?php if(count($data["categories"]) > 0): ?>
                    <?php if (isset($component)) { $__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142 = $attributes; } ?>
<?php $component = App\View\Components\General\SearchBar::resolve(['placeholder' => ''.e(trans('products_categories.search_placeholder')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.search-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\SearchBar::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142)): ?>
<?php $attributes = $__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142; ?>
<?php unset($__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142)): ?>
<?php $component = $__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142; ?>
<?php unset($__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <?php if(count($data["categories"]) > 0): ?>
                                <table class="table">
                                    <caption style="visibility: hidden"></caption>
                                    <thead class="thead">
                                    <tr class="custom-border-bottom-css">
                                        <th scope="col"><?php echo e(__('Name')); ?></th>
                                        <th scope="col" class="text-end"><?php echo e(__('Actions')); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                     <tr>
                                        <td colspan="2" class="firstTree" id="firstTree"></td>
                                     </tr>
                                    </tbody>
                                </table>
                        <?php else: ?>
                            <?php if (isset($component)) { $__componentOriginal9c590450895f60ce3af8182e3a1a843e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c590450895f60ce3af8182e3a1a843e = $attributes; } ?>
<?php $component = App\View\Components\General\EmptyPage::resolve(['description' => ''.e(trans('products_categories.page_empty')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.empty-page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\EmptyPage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $attributes = $__attributesOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $component = $__componentOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__componentOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </div>
                    
                </div>
            </div>

    <!-- Modal DELETE-->
    <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel"
         aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h3 class="modal-title" id="exampleModalLabel"><?php echo e(trans('products_categories.modal_title')); ?></h3>
                    <button type="button" class="btn-sm border-0 bg-white" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true" class="fs-24">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="Roboto regular"><?php echo e(trans('products_categories.modal_description')); ?>

                        
                    </p>

                    <div class="modal-footer pt-3">
                        <button type="button" data-bs-dismiss="modal" id="delete-cancel-btn"
                                class="btn btn-light float-left shadow-sm border">
                            <?php echo e(trans('products_categories.cancel_btn')); ?>

                        </button>
                        <form action="#" id="delete-category" method="post">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <span id="pro_cat_del_btn" class=" btn btn-danger delete-category"
                                  onclick="del(this)" data-id=""><?php echo e(trans('products_categories.delete_btn')); ?></span>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer_scripts'); ?>
    <script type="text/javascript">
        function del(e) {
            attrid = $(e).attr('data-id');
            var form = document.getElementById('delete-category');
            form.setAttribute('action', 'categories/' + attrid);
            form.submit();
        }

    </script>

    <script type="text/javascript">
        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        $('#add-modal').modal('show')
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </script>

    <script>
        /**
         * Created by Francis Pogulis
         * <EMAIL>
         * Created for a simple, yet functional UI
         *
         * Obviosly improvements should be made
         * jQuery is required
         */
        class DinampTreeEditor {
            mainNode;     //selector
            uuid;
            contextMenu;  //jQuery node
            selectedItem; //jQuery node
            options;      //{}

            //TODO add max capacity and tags, stackabel=bool
            constructor(mainNode) {
                this.uuid = this.newuuid();
                if(!$(mainNode).hasClass("jsTree"))
                    $(mainNode).addClass("jsTree");//make sure styles are accurate

                $(mainNode).attr("ui-uuid", this.uuid);
                this.mainNode = mainNode + "[ui-uuid='"+this.uuid+"']";

                //TODO translte contextmenu items
                this.contextMenu = $("<div class='jsTreeContextMenu' ui-uuid='"+this.uuid+"'><p>Delete</p></div>");//<p>Move up</p><p>Move down</p>
                this.contextMenu.insertAfter($(this.mainNode));

                //one-off listeners:

                let jsTree = this;
                $(document).on("mousedown",function(e){

                    if(!$(e.target).hasClass("afterIcon") && !$(e.target).hasClass("jsTreeContextMenu") && !($(e.target).parents(".jsTreeContextMenu").length > 0)){
                        jsTree.contextMenu.hide();
                    }
                });

                //initial options:
                this.options = {
                    checkboxes: false,
                    radios: false,
                    editable: true
                };
                //this.rebindListeners();
            }

            setData(data) {
                $(this.mainNode).empty();
                data.forEach(element => this.addElement(element, $(this.mainNode)));
                //TODO Optimize this line here
                //$(this.mainNode).css({width: $(this.mainNode).width() + "px"}); //makes the width fixed, so when collaped it stays the same width
                this.rebindListeners();
                return this;
            }
            set(opts) {
                let jsTree = this;
                jsTree.options = opts;
                if(opts.extended === false) {
                    $(this.mainNode+" .preIcon").each(function(){
                        if($(this).hasClass("arrowDown")) $(this).addClass("arrowRotate");
                    });
                    $(this.mainNode+" .childGroup").hide();
                }
                if(opts.checkboxes === true) {
                    $(this.mainNode+" .preIcon").each(function(){
                        $(this).removeClass("arrowDown");
                        $(this).addClass("checkboxIcon");
                    });
                    jsTree.options.radios = false;
                } else if(opts.radios === true) {
                    $(this.mainNode+" .preIcon").each(function(){
                        if(!$(this).hasClass("arrowDown")) {
                            $(this).addClass("radiobtnIcon");
                        }
                    });
                    jsTree.options.checkboxes = false;
                } else {
                    jsTree.options.radios = false;
                    jsTree.options.checkboxes = false;
                }

                if(opts.editable === false) {
                    $(this.mainNode+" p").removeAttr("contenteditable");
                    $(this.mainNode+" .afterIcon").hide();
                } else {
                    jsTree.options.editable = true;

                }

                this.rebindListeners();
                return this;
            }

            getData(){
                let jsTree = this;
                var retVal = [];
                $(this.mainNode).subs().each(function(){
                    jsTree.pushData(retVal, jsTree, $(this));
                });
                return retVal;
            }

            pushData(parentData, jsTree, subject) {
                if(subject.is("ul")) return;
                if(subject.is(".itemParent")) {
                    let currentItem = {
                        title: subject.find("p").text()
                    };
                    if(subject.find(".preIcon").hasClass("checked")) currentItem.checked = true;
                    if(subject.next().is("ul")) {
                        currentItem.subs = [];
                        $(subject.next()).subs().each(function(){
                            jsTree.pushData(currentItem.subs, jsTree, $(this).find(".itemParent").eq(0));
                        });
                    }

                    parentData.push(currentItem);
                }
            }

            addElement(el, parentNode = null,id=null) {
                var $newNode;
                $(".childGroup").hide();
                if(parentNode.is("ul")) {
                    $newNode = $(`<li class='item' id='${el.value}'><div class='itemParent' id='${el.value}'><span class='preIcon'></span><div class="contenteditable"><p contenteditable='false'>${el.name}</p></div><span class='fa-regular fa-pen-to-square fs-20 text-primary'></span><span data-id="${el.value}" data-bs-toggle="modal" data-bs-target="#delete-modal"  class='fa-regular ms-1 fa-trash-can fs-20 text-danger'></span></div></li>`);
                }else {
                    $newNode = $(`<div class='itemParent' id='${el.value}'><span class='preIcon' id='${el.value}'></span><div class="contenteditable"><p contenteditable='false'>${el.name}</p></div><span class='fa-regular fa-pen-to-square fs-20 text-primary'></span><span data-id="${el.value}" data-bs-toggle="modal" data-bs-target="#delete-modal" class='fa-regular ms-1 fa-trash-can fs-20 text-danger' ></span></div>`);
                } //if(parentNode == null) parentNode = $(this.mainNode);
                parentNode.append($newNode);
                if(el.checked === true || el.checked === "true") $newNode.find(".preIcon").addClass("checked");
                if(el.children !== undefined) {
                    $newNode.find(".preIcon").addClass("arrowDown arrowRotate");
                    var $chContainer = $("<ul class='childGroup'></ul>");
                    if(parentNode.is("ul"))
                        $newNode.append($chContainer);
                    else
                        $(this.mainNode).append($chContainer);
                    el.children.forEach(element => this.addElement(element, $chContainer));
                }
            }


//EVENT LISTENERS BEGIN HERE
            unbindListenders() {
                $(this.mainNode+" p").off();
                $(this.mainNode+" .preIcon").off();
                $(this.mainNode+" .afterIcon").off();
                $(".jsTreeContextMenu[ui-uuid='"+this.uuid+"'] p").off();
            }

            rebindListeners (jsTree = this) {
                jsTree.unbindListenders();
                $(this.mainNode+" p").keydown(function(e){
                    if(e.keyCode == 13) {//code here is duplicate from below
                        jsTree.selectedItem = $(":focus").closest(".itemParent");
                        if(jsTree.selectedItem.parent().is("li")) {
                            console.log("mouse downf")
                            //  var $newNode = $("<li class='item'><div class='itemParent'><span class='preIcon'></span><p contenteditable='true'>New item</p><span class='afterIcon'></span></div></li>");
                            // $newNode.insertAfter(jsTree.selectedItem.parent());
                        }
                        else if(jsTree.selectedItem.next().length > 0 && jsTree.selectedItem.next().is(".childGroup")) {
                            $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='fa-regular fa-pen-to-square fs-20 text-primary'></span><span class='fa-regular ms-1 fa-trash-can fs-20 text-danger'></span></div>");
                            $newNode.insertAfter(jsTree.selectedItem.next());
                        } else {
                            $newNode = $("<div class='itemParent'><span class='preIcon'></span><div><p contenteditable='true'>New item</p></div><span class='fa-regular fa-pen-to-square fs-20 text-primary'></span><span class='fa-regular ms-1 fa-trash-can fs-20 text-danger'></span></div>");
                            $newNode.insertAfter(jsTree.selectedItem);
                        }
                        jsTree.rerender(jsTree);
                        return false;
                    }
                });

                $(this.mainNode+" p").on('blur', function () {
                    jsTree.options.onchange(jsTree);
                });

                $(this.mainNode+" .preIcon").on('click', function(){
                    if($(this).hasClass("arrowDown") && !$(this).hasClass("arrowRotate")) { //children are expanded must retract
                        if($(this).parent().parent().is("li"))
                            $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                        else
                            $(this).parent().next().animate({height: "toggle"}, 400);
                        $(this).addClass("arrowRotate");
                    } else if($(this).hasClass("arrowDown") && $(this).hasClass("arrowRotate")) { //children are retracted
                        if($(this).parent().parent().is("li"))
                            $(this).parent().parent().find(".childGroup").eq(0).animate({height: "toggle"}, 400);
                        else
                            $(this).parent().next().animate({height: "toggle"}, 400);
                        $(this).removeClass("arrowRotate");
                    } else if($(this).hasClass("checkboxIcon")) {
                        if($(this).hasClass("checked")) $(this).removeClass("checked");
                        else $(this).addClass("checked");
                    } else if($(this).hasClass("radiobtnIcon")) {
                        $(jsTree.mainNode+" .preIcon").removeClass("checked");
                        $(this).addClass("checked");
                    }

                    if($(this).hasClass("checkboxIcon") || $(this).hasClass("radiobtnIcon")) {
                        if(jsTree.options.oncheck !== undefined) {
                            let pathToDis = [];
                            var curItem = $(this).parent();
                            while(curItem.parent().is("li") || curItem.parent().is(jsTree.mainNode)) {
                                pathToDis.unshift(curItem.find("p").text());
                                curItem = curItem.parent().parent().prevAll().eq(0);
                            }
                            jsTree.options.oncheck($(this).hasClass("checked"), $(this).parent().find("p").text(), pathToDis);
                        }
                        if(jsTree.options.onchange !== undefined) {
                            jsTree.options.onchange(jsTree);
                        }
                    }
                });

                $(this.mainNode+" .fa-trash-can").on('click', function(){
                     $("#pro_cat_del_btn").attr('data-id',$(this).parents('.itemParent').attr('id'));
                });
                    $(this.mainNode+" .fa-pen-to-square").on('click', function(){
                        const id=$(this).parents('.itemParent').attr('id');
                        var url = '<?php echo e(route("categories.edit", ":id")); ?>';
                        url = url.replace(':id', id);
                        location.href = url;
                    });
                }

                rerender(jsTree = this) {
                    if(jsTree.options.checkboxes === true) {
                        $(jsTree.mainNode+" .preIcon").each(function(){
                            if(!$(this).hasClass("arrowDown")) {
                                $(this).addClass("checkboxIcon");
                            }
                        });
                        jsTree.options.radios = false;
                    } else if(jsTree.options.radios === true) {
                        $(jsTree.mainNode+" .preIcon").each(function(){
                            if(!$(this).hasClass("arrowDown")) {
                                $(this).addClass("radiobtnIcon");
                            }
                        });
                    } else {
                        $(jsTree.mainNode+" .itemParent").each(function(){//TODO optimize, when delete delay required, otherwise not
                            if($(this).next().is("ul")){
                                if($(this).next().children().length > 0) {
                                    $(this).find(".preIcon").eq(0).addClass("arrowDown");
                                    if($(this).next().is(":visible"))
                                    { $(this).find(".preIcon").eq(0).removeClass("arrowRotate");}
                                    else
                                        $(this).find(".preIcon").eq(0).addClass("arrowRotate");
                                } else
                                    $(this).find(".preIcon").eq(0).removeClass("arrowDown");
                            } else
                                $(this).find(".preIcon").eq(0).removeClass("arrowDown");
                        });
                    }
                    if(jsTree.options.onchange !== undefined) {
                        jsTree.options.onchange(jsTree);
                    }

                    jsTree.rebindListeners(jsTree);
                }

                newuuid() {
                    return ([1e7]+-1e11).replace(/[018]/g, c =>
                        (c ^ crypto.getRandomValues(new Uint8Array(1))[0] & 15 >> c / 4).toString(16)
                    );
                }
            }

            let data = <?php echo json_encode($category_data, 15, 512) ?>;
            $(".firstTree").each(function( index ) {
                const divIdAttribute=$('.firstTree')[index];
                const divId=$(divIdAttribute).attr('id');
                new DinampTreeEditor(`#${divId}`).setData(data);
            });

            class EnhancedDinampTreeEditor extends DinampTreeEditor {
                constructor(mainNode, itemsPerPage) {
                    super(mainNode);
                    this.itemsPerPage = itemsPerPage;
                    this.currentPage = 1;
                    this.totalPages = 1; // Default value
                    this.paginationContainer = null;
                    this.nextButton = null;
                    this.prevButton = null;
                }

                setDataWithPagination(data) {
                    this.data = data;
                    this.totalPages = Math.ceil(data.length / this.itemsPerPage);
                    this.gotoPage(1);
                    this.createPaginationControls();
                    return this;
                }

                createPaginationControls() {
                    if (this.data.length > 8) {
                        // Create the pagination controls container
                        this.paginationContainer = document.createElement('div');
                        this.paginationContainer.classList.add('pagination-container');

                        // Create Next and Prev buttons
                        this.nextButton = document.createElement('button');
                        this.nextButton.textContent = '>';
                        this.nextButton.addEventListener('click', () => this.next());

                        this.prevButton = document.createElement('button');
                        this.prevButton.textContent = '<';
                        this.prevButton.addEventListener('click', () => this.prev());

                        // Append buttons to the container
                        this.paginationContainer.appendChild(this.prevButton);
                        this.paginationContainer.appendChild(this.nextButton);

                        // Convert mainNode to a jQuery object
                        const $mainNode = $(this.mainNode);

                        const tableElement = $mainNode.closest('table')[0];
                        tableElement.insertAdjacentElement('afterend', this.paginationContainer);

                        // Update pagination controls after rendering
                        this.updatePaginationControls();
                        this.updateNumberedPageLinks();
                    }
                }
                updateNumberedPageLinks() {
                    // Remove any existing numbered page links
                    const numberedLinks = this.paginationContainer.querySelectorAll('.numbered-page');
                    numberedLinks.forEach(link => link.remove());

                    // Create and append numbered page links
                    for (let page = 1; page <= this.totalPages; page++) {
                        const numberedLink = document.createElement('span');
                        numberedLink.classList.add('numbered-page');
                        numberedLink.textContent = page;
                        numberedLink.addEventListener('click', () => {
                            $(".numbered-page").removeClass("active");
                            numberedLink.classList.add("active");

                            this.gotoPage(page)
                        });
                        if (page === this.currentPage) {
                            numberedLink.classList.add('active');
                        }
                        this.paginationContainer.insertBefore(numberedLink, this.nextButton);
                    }
                }
                updatePaginationControls() {
                    if (this.nextButton && this.prevButton) {
                        this.nextButton.disabled = this.currentPage >= this.totalPages;
                        this.prevButton.disabled = this.currentPage <= 1;
                    }
                }

                gotoPage(page) {
                    this.currentPage = page;
                    const startIndex = (page - 1) * this.itemsPerPage;
                    const endIndex = startIndex + this.itemsPerPage;
                    const dataPage = this.data.slice(startIndex, endIndex);
                    this.setData(dataPage);
                    this.updatePaginationControls();
                }

                next() {
                    if (this.currentPage < this.totalPages) {
                        this.gotoPage(this.currentPage + 1);
                        this.updateNumberedPageLinks();
                    }
                }

                prev() {
                    if (this.currentPage > 1) {
                        this.gotoPage(this.currentPage - 1);
                        this.updateNumberedPageLinks();
                    }
                }
            }

            // Usage
            const itemsPerPage = 8;
            const enhancedTreeEditor = new EnhancedDinampTreeEditor('.firstTree', itemsPerPage);
            enhancedTreeEditor.setDataWithPagination(data);

        </script>
    <?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/categories/view.blade.php ENDPATH**/ ?>