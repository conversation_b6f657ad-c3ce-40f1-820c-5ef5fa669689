{"__meta": {"id": "01JZ50T0824X75QHMR2MPX8P35", "datetime": "2025-07-02 07:32:19", "utime": **********.333231, "method": "GET", "uri": "/api/2024-12/organization/billing/status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.444988, "end": **********.333264, "duration": 0.8882761001586914, "duration_str": "888ms", "measures": [{"label": "Booting", "start": **********.444988, "relative_start": 0, "end": **********.970821, "relative_end": **********.970821, "duration": 0.****************, "duration_str": "526ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.970834, "relative_start": 0.***************, "end": **********.333267, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.987079, "relative_start": 0.***************, "end": **********.991647, "relative_end": **********.991647, "duration": 0.0045680999755859375, "duration_str": "4.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.29956, "relative_start": 0.***************, "end": **********.300225, "relative_end": **********.300225, "duration": 0.0006649494171142578, "duration_str": "665μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.326351, "relative_start": 0.****************, "end": **********.326493, "relative_end": **********.326493, "duration": 0.00014209747314453125, "duration_str": "142μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/organization/billing/status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "as": "organization.billing.status", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00525, "accumulated_duration_str": "5.25ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.081412, "duration": 0.00449, "duration_str": "4.49ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 85.524}, {"sql": "select * from `organizations` where `id` is null and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.2883148, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "OrganizationController.php:177", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=177", "ajax": false, "filename": "OrganizationController.php", "line": "177"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.524, "width_percent": 14.476}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "27K9BQkxBeypYSnYlTYXl28C9XVvij8Z3ei58vwt", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization/billing/status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null"}, "request": {"data": {"status": "404 Not Found", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus", "uri": "GET api/2024-12/organization/billing/status", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=174\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:174-218</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4aed14-71e0-422d-aff7-61201fa7ebd9\" target=\"_blank\">View in Telescope</a>", "duration": "893ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1423071821 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1423071821\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1886380238 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1886380238\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-356677406 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5zeG45eU9zTGUrZldVbDZicngxelE9PSIsInZhbHVlIjoiMHlWNjE4Snhzb0taMTIrUzF2RVlsdHM3elJNY3VNQUNPblcrcExZeXozWUVvL21IOXZTWUg0V3RhVXhNeWlrNnhUOEFib1pZTTJicTlBRm9udmVucVc3aGFDU3J3RS9VeTNNVzY1cHRVVzhPRlNGVnhKUTFCYnR2TGhvZUM4QmciLCJtYWMiOiJkMzgwMjAyNDgzNGU2NzgyYjI0ZmQxMjYwZjdkMmZkMmFkMzQ3ZDU5MmY1NzE3YTc0YzUwYTU0NmI3MDRiNjUyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IjdFdXdXUmp5bDdNT1RtbCtOejRlaEE9PSIsInZhbHVlIjoibzR1b2FKdExReXR2ZkdqYUpKeXBLNVVSL3B1NHdmM0N1U1MvSlFJTjhHaHFQRWg1UjUzSURDcXVDZ1p3blhRRHFyWW9MTndlUlNEbWs0SmMrUWFrWTFSZisrRHZaaERoSGQrWmFHWjlGV2JFNFl1RWhVeDR2MnpJS2NaUXd6QUVYUEtuVUpUQmdXWTl1OHF5NnVVaWt3PT0iLCJtYWMiOiI0ZTRjMzM1NDliYjEyMGIzNmVmMTUzMjFhMjQ0M2QzM2U0NWZmNWFkZTVmYzVkMzY4MGQxNTQwMGFlZTM5MWRiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ik5zeG45eU9zTGUrZldVbDZicngxelE9PSIsInZhbHVlIjoiMHlWNjE4Snhzb0taMTIrUzF2RVlsdHM3elJNY3VNQUNPblcrcExZeXozWUVvL21IOXZTWUg0V3RhVXhNeWlrNnhUOEFib1pZTTJicTlBRm9udmVucVc3aGFDU3J3RS9VeTNNVzY1cHRVVzhPRlNGVnhKUTFCYnR2TGhvZUM4QmciLCJtYWMiOiJkMzgwMjAyNDgzNGU2NzgyYjI0ZmQxMjYwZjdkMmZkMmFkMzQ3ZDU5MmY1NzE3YTc0YzUwYTU0NmI3MDRiNjUyIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IjMwdjFuSCt2UkQzNTJ3RWtuOUQyYUE9PSIsInZhbHVlIjoiYWl4Z1MzbFhiK3VRdXROWGpxZFMwNFl5RXZtUUF0NjV6enAvZ1BqYTlNUENqRWZLU2h1UWNZMWg5aTdQWkJOSHFyWkR1S2FYbHdrcDRRSnpDUnFPNDJ3ckw5dnlWa2lVY3JWMk5NbmdjekVraUwxckljeWUyRmh0UE9JVy9iWDMiLCJtYWMiOiI5ZDgxNDJkMDhjZWFlYTc5ZjIyZjczYThjNWVkZjA3Mzg2NjAxZTBiMjE0MGRkNDkzZjBiNzI2NGRkMTkyNmY2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-356677406\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1458319521 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|4gSMW5C3jpJL06LZ3KwxPstmT881TikM3cfXR2CuxNxGXZi504fXE5SD9EZN|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27K9BQkxBeypYSnYlTYXl28C9XVvij8Z3ei58vwt</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">gt9t9h9opwJ0eGblfTsIhnfaGUc9n3TcmvQB6EdO</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1458319521\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1787569164 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:32:19 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ik9sVVZJaExGSGliOWNJNFg0bkJ4Z1E9PSIsInZhbHVlIjoiOElwY01GZFJsYVk3Z2ZEZjhwQzVYc3ZwdkpJc1BxbXRZeDBNckZxS0d1dDFrZ29zMEMxeEttZTJvUFBIb2hGVHVGU05BQ2R5UUZIam1pWXAxV2NuMTMzMVRyYjdoeUg1eER6ekkxb2JacjEzTUpKK01XaVYvQS94K1lDekIvNEYiLCJtYWMiOiI4N2JhYTI0YjA4NTcyZjJiOTFiNTQ1YjNkYjViMTI4OTk3OWQwODEwZWFhZGFjZmFhZTlkYjY2ZTEyY2ZmMWU4IiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:32:19 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6Im9JVlJ1VTJDckFkbG12ZDF5ZnRFZUE9PSIsInZhbHVlIjoiY1RYT281QzZIakxoay9YNndCVkkzbFJOT1Y5SHZDZkdOYW1qR090VXlqaExySnFUcDVDenJvN3hRUElWcmhzR2hVaXV6SWxLaE41NEkyakJRSG0vU0dueFBkWXVXQzNucTJtclRTaEsrVzNyaHdNb0FhYXlqanFEVEZJWnF1VkEiLCJtYWMiOiJiYjliMjFiYmU1NTJkOWYyOGM0NTlmNzIwZjlhOThkOTI3YzZmYjZkNDYwZjlkM2U2MjM3NzAzNDc4NDQ1ZTBkIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:32:19 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ik9sVVZJaExGSGliOWNJNFg0bkJ4Z1E9PSIsInZhbHVlIjoiOElwY01GZFJsYVk3Z2ZEZjhwQzVYc3ZwdkpJc1BxbXRZeDBNckZxS0d1dDFrZ29zMEMxeEttZTJvUFBIb2hGVHVGU05BQ2R5UUZIam1pWXAxV2NuMTMzMVRyYjdoeUg1eER6ekkxb2JacjEzTUpKK01XaVYvQS94K1lDekIvNEYiLCJtYWMiOiI4N2JhYTI0YjA4NTcyZjJiOTFiNTQ1YjNkYjViMTI4OTk3OWQwODEwZWFhZGFjZmFhZTlkYjY2ZTEyY2ZmMWU4IiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:32:19 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6Im9JVlJ1VTJDckFkbG12ZDF5ZnRFZUE9PSIsInZhbHVlIjoiY1RYT281QzZIakxoay9YNndCVkkzbFJOT1Y5SHZDZkdOYW1qR090VXlqaExySnFUcDVDenJvN3hRUElWcmhzR2hVaXV6SWxLaE41NEkyakJRSG0vU0dueFBkWXVXQzNucTJtclRTaEsrVzNyaHdNb0FhYXlqanFEVEZJWnF1VkEiLCJtYWMiOiJiYjliMjFiYmU1NTJkOWYyOGM0NTlmNzIwZjlhOThkOTI3YzZmYjZkNDYwZjlkM2U2MjM3NzAzNDc4NDQ1ZTBkIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:32:19 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1787569164\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-595270783 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">27K9BQkxBeypYSnYlTYXl28C9XVvij8Z3ei58vwt</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8000/api/2024-12/organization/billing/status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-595270783\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "404 Not Found", "full_url": "http://localhost:8000/api/2024-12/organization/billing/status", "action_name": "organization.billing.status", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@billingStatus"}, "badge": "404 Not Found"}}