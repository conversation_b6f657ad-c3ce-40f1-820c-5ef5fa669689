<?php ?>

<?php if(Request::segment(4) == 'edit'): ?>
    <?php $__env->startSection('titles','Edit Brand'); ?>
<?php else: ?>
    <?php $__env->startSection('titles','Add Brands'); ?>
<?php endif; ?>
<?php $__env->startSection('content'); ?>
    <div>

        <?php if (isset($component)) { $__componentOriginal7a044565d18b640bc08243f7f8ff3e54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a044565d18b640bc08243f7f8ff3e54 = $attributes; } ?>
<?php $component = App\View\Components\Products\AddPageTitle::resolve(['name' => ''.e(trans('products_brands.page_title')).'','routes' => route('brands.index')] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.add-page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\AddPageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'false']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a044565d18b640bc08243f7f8ff3e54)): ?>
<?php $attributes = $__attributesOriginal7a044565d18b640bc08243f7f8ff3e54; ?>
<?php unset($__attributesOriginal7a044565d18b640bc08243f7f8ff3e54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a044565d18b640bc08243f7f8ff3e54)): ?>
<?php $component = $__componentOriginal7a044565d18b640bc08243f7f8ff3e54; ?>
<?php unset($__componentOriginal7a044565d18b640bc08243f7f8ff3e54); ?>
<?php endif; ?>
        <div class="row mt-4">
            <div class="col-12 col-md-9 col-lg-9 col-xl-5">
                <form id="pro_bran_create_form" class="formStyle"
                      action="<?php echo e(isset($brand) ? route('brands.update',$brand->id) : route('brands.store')); ?>"
                      method="POST">
                    <?php echo csrf_field(); ?>
                    <?php if(isset($brand)): ?>
                        <?php echo method_field('PUT'); ?>
                        <input type="hidden" name="id" value="<?php echo e($brand->id); ?>">
                    <?php endif; ?>
                    <div class="form-group">
                        <label for="name"><?php echo e(trans('products_brands_create.brand_name')); ?>&nbsp;
                            <span class="text-danger">*</span>
                        </label>
                        <input type="text"
                               class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="name"
                               name="name"
                               value="<?php echo e(isset($brand) ? $brand->name : old('name')); ?>" autofocus>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger">
                            <small><?php echo e($message); ?></small>
                            </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="form-group mb-4 mt-40">
                        <div class="d-flex justify-content-end">
                            <a href="<?php echo e(route('brands.index')); ?>" class="btn btn-outline-danger" id="cancel-btn">
                                    <?php echo e(trans('products_brands_create.cancel_btn')); ?>

                            </a>
                            <button type="submit" id="pro_bran_create_btn" class="btn btn-primary ms-2">
                                <?php echo e(trans('products_brands_create.save_btn')); ?>

                            </button>

                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer_scripts'); ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/brands/add.blade.php ENDPATH**/ ?>