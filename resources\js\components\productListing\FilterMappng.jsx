import React, { useState, useEffect } from "react";
import { Card, Select, Input, message, TreeSelect, Button, Spin } from "antd";
const { TextArea } = Input;
import { DeleteOutlined, LoadingOutlined } from "@ant-design/icons";
import axios from "axios";
import { v4 as uuidv4 } from "uuid"; // For generating unique keys

const FilterMapping = ({ onFiltersFinalized, setFilterMapping, savedFilters }) => {
    const [attributes, setAttributes] = useState([]);
    const [formulas, setFormulas] = useState({});
    const [filters, setFilters] = useState([
        [
            {
                id: uuidv4(), // Generate a unique ID
                selectedAttribute: null,
                formulaOptions: [],
                isFormulaSelectDisabled: true,
                selectedFormula: null,
                selectedTreeValue: [],
                inputValue: "",
                selectedDropdownValue: null,
                treeData: [], // Initialize treeData for each row
            },
        ],
    ]);
    const [filterResults, setFilterResults] = useState([]); // Parent array to store filter results
    const [loading, setLoading] = useState(true);
    const [mappingLoading, setMappingLoading] = useState(true);

    useEffect(() => {
        fetchData(); // Fetch data first
    }, []);

    useEffect(() => {
        if (attributes.length > 0) {
            loadSavedFilters(); // Load filters after attributes are fetched
        }
    }, [attributes]);

    // Listen for localStorage changes and custom events to reset filters when cleared
    useEffect(() => {
        const resetToInitialState = () => {
            if (attributes.length > 0) {
                setFilters([
                    [
                        {
                            id: uuidv4(),
                            selectedAttribute: null,
                            formulaOptions: [],
                            isFormulaSelectDisabled: true,
                            selectedFormula: null,
                            selectedTreeValue: [],
                            inputValue: "",
                            selectedDropdownValue: null,
                            treeData: [],
                        },
                    ],
                ]);
                onFiltersFinalized([]);
            }
        };

        const handleStorageChange = () => {
            const currentStoredFilters = localStorage.getItem("filters");
            if (!currentStoredFilters) {
                resetToInitialState();
            }
        };

        const handleFiltersCleared = () => {
            resetToInitialState();
        };

        window.addEventListener('storage', handleStorageChange);
        window.addEventListener('filtersCleared', handleFiltersCleared);

        return () => {
            window.removeEventListener('storage', handleStorageChange);
            window.removeEventListener('filtersCleared', handleFiltersCleared);
        };
    }, [attributes, onFiltersFinalized]);

    const fetchData = () => {
        setLoading(true);
        axios
            .get(`api/${import.meta.env.VITE_API_VERSION}/filters`, {
                headers: {
                    "Content-Type": "application/json",
                },
            })
            .then((response) => {
                setAttributes(response.data.data.attributes);
                setFormulas(response.data.data.formulas);
            })
            .catch((error) => {
                message.error("Server Busy, Try again Later");
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const loadSavedFilters = async () => {
        setMappingLoading(true);
        const savedFilters = localStorage.getItem("filters");
        if (savedFilters && savedFilters.trim() !== "") {
            const parsedFilters = JSON.parse(savedFilters);

            const formattedFilters = parsedFilters.map(
                (filterGroup) =>
                    filterGroup
                        .map((filterItem, index) => {
                            if (!filterItem || !filterItem.attribute) {
                                console.error(`No matching attribute found for filterItem at index ${index}:`, filterItem);
                                return null;
                            }

                            let parsedAttribute = null;

                            const foundAttribute = attributes.find((attrGroup) =>
                                attrGroup.options.find((option) => {
                                    try {
                                        const optionValue = JSON.parse(option.value);
                                        const filterItemAttribute = filterItem.attribute || "";
                                        const optionValueAttribute = optionValue.value || "";

                                        // Check for a match
                                        if (optionValueAttribute === filterItemAttribute) {
                                            parsedAttribute = optionValue; // Store the matched value as parsedAttribute
                                            return true; // Return true to stop the search after the match
                                        }

                                        return false;
                                    } catch (error) {
                                        console.error("Error parsing option value:", error);
                                        return false;
                                    }
                                })
                            );

                            if (!foundAttribute || !parsedAttribute) {
                                console.error(`No matching attribute found for filterItem at index ${index}:`, filterItem);
                                return null;
                            }

                            const isDefinedFormula = filterItem.formula === "is_defined" || filterItem.formula === "is_not_defined";

                            return {
                                id: uuidv4(),
                                selectedAttribute: parsedAttribute,
                                selectedFormula: filterItem.formula || null,
                                inputValue: isDefinedFormula ? "" : typeof filterItem.value === "string" ? filterItem.value : "",
                                selectedTreeValue: isDefinedFormula ? [] : Array.isArray(filterItem.value) ? filterItem.value : [],
                                selectedDropdownValue: isDefinedFormula
                                    ? null
                                    : typeof filterItem.value === "string"
                                    ? filterItem.value
                                    : null,
                                json_data: filterItem.json_data || parsedAttribute?.json_data || null,
                                formulaOptions: formulas[parsedAttribute?.type] || [],
                                isFormulaSelectDisabled: false,
                                treeData: [], // This will be populated after API calls
                            };
                        })
                        .filter(Boolean) // Remove any null/undefined items
            );
            setFilters(formattedFilters); // Set the initial filters before making API calls

            // Now loop through the filters and make API calls for those with `api_url`
            await makeApiCallsForFilters(formattedFilters);
        } else {
            // If no saved filters, reset to initial state
            setFilters([
                [
                    {
                        id: uuidv4(),
                        selectedAttribute: null,
                        formulaOptions: [],
                        isFormulaSelectDisabled: true,
                        selectedFormula: null,
                        selectedTreeValue: [],
                        inputValue: "",
                        selectedDropdownValue: null,
                        treeData: [],
                    },
                ],
            ]);
        }
        setMappingLoading(false);
    };

    const makeApiCallsForFilters = async (formattedFilters) => {
        setLoading(true);

        // Group filters by API URL to avoid duplicate calls
        const apiGroups = {};

        formattedFilters.forEach((filterGroup, i) => {
            filterGroup.forEach((filterItem, j) => {
                const { selectedAttribute } = filterItem;
                if (selectedAttribute?.api_url) {
                    if (!apiGroups[selectedAttribute.api_url]) {
                        apiGroups[selectedAttribute.api_url] = [];
                    }
                    apiGroups[selectedAttribute.api_url].push({ i, j });
                }
            });
        });

        const promises = Object.keys(apiGroups).map(async (api_url) => {
            try {
                const response = await axios.get(`/api/${import.meta.env.VITE_API_VERSION}/${api_url}`, {
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                const newTreeData = response.data.data;
                console.log(`API response for ${api_url}:`, newTreeData);

                // Update the treeData for all filters using this api_url
                apiGroups[api_url].forEach(({ i, j }) => {
                    formattedFilters[i][j].treeData = newTreeData;
                });
            } catch (error) {
                message.error("Server Busy, Try again Later");
                console.error(`Error fetching API data for ${api_url}:`, error);
            }
        });

        // Run all API calls in parallel
        await Promise.all(promises);

        setLoading(false);
        setFilters(formattedFilters); // Update the filters with the new API data
    };

    const handleChange = async (value, cardIndex, rowIndex) => {
        const selectedAttribute = JSON.parse(value);
        const { type, family, json_data, api_url } = selectedAttribute; // Destructure json_data if it exists

        const options = formulas[type] || [];

        let newTreeData = [];
        if (api_url) {
            setLoading(true);
            try {
                const response = await axios.get(`/api/${import.meta.env.VITE_API_VERSION}/${api_url}`, {
                    headers: {
                        "Content-Type": "application/json",
                    },
                });
                newTreeData = response.data.data;
            } catch (error) {
                message.error("Server Busy, Try again Later");
            } finally {
                setLoading(false);
            }
        }

        const updatedFilters = [...filters];
        updatedFilters[cardIndex][rowIndex] = {
            ...updatedFilters[cardIndex][rowIndex],
            selectedAttribute,
            formulaOptions: options,
            isFormulaSelectDisabled: false,
            selectedFormula: null,
            treeData: newTreeData, // Store the fetched data independently for this row
            selectedTreeValue: [], // Clear any previous selection
        };

        // Add json_data to the filter object if it exists
        if (json_data) {
            updatedFilters[cardIndex][rowIndex].json_data = json_data;
        }
        if (family) {
            updatedFilters[cardIndex][rowIndex].family = family;
        }

        setFilters(updatedFilters);
    };

    const handleFilterChange = (type, value, cardIndex, rowIndex) => {
        const updatedFilters = [...filters];

        switch (type) {
            case "formula":
                updatedFilters[cardIndex][rowIndex].selectedFormula = value;
                break;
            case "treeSelect":
                updatedFilters[cardIndex][rowIndex].selectedTreeValue = value;
                break;
            case "input":
                updatedFilters[cardIndex][rowIndex].inputValue = value.target ? value.target.value : value;
                break;
            case "dropdown":
                updatedFilters[cardIndex][rowIndex].selectedDropdownValue = value;
                break;
            default:
                return; // Return if an unsupported type is passed
        }

        setFilters(updatedFilters);
        onFiltersFinalized(updatedFilters);

        // Clear localStorage when filters are modified to ensure fresh state
        // This prevents the bug where old filters override new ones
        if (updatedFilters.length > 0 && updatedFilters[0].length > 0) {
            const hasValidFilters = updatedFilters.some(filterGroup =>
                filterGroup.some(filter =>
                    filter.selectedAttribute && filter.selectedFormula
                )
            );
            if (!hasValidFilters) {
                localStorage.removeItem("filters");
            }
        }
    };

    const addNewRow = (cardIndex) => {
        addNewFilter(cardIndex);
    };

    const addNewFilter = (cardIndex) => {
        const updatedFilters = [...filters];
        updatedFilters[cardIndex] = [
            ...updatedFilters[cardIndex],
            {
                id: uuidv4(), // Ensure each new row has a unique ID
                selectedAttribute: null,
                formulaOptions: [],
                isFormulaSelectDisabled: true,
                selectedFormula: null,
                selectedTreeValue: [],
                inputValue: "",
                selectedDropdownValue: null,
                treeData: [], // Initialize treeData for new filter row
            },
        ];
        setFilters(updatedFilters);
    };

    const addNewCard = () => {
        const newFilterArray = [
            {
                id: uuidv4(), // Ensure the first row in the new card has a unique ID
                selectedAttribute: null,
                formulaOptions: [],
                isFormulaSelectDisabled: true,
                selectedFormula: null,
                selectedTreeValue: [],
                inputValue: "",
                selectedDropdownValue: null,
                treeData: [], // Initialize treeData for new card's first row
            },
        ];

        setFilters([...filters, newFilterArray]);
        setFilterResults([...filterResults, []]);
    };

    const handleDeleteFilter = (cardIndex, rowIndex) => {
        const updatedFilters = [...filters];
        updatedFilters[cardIndex].splice(rowIndex, 1);

        if (updatedFilters[cardIndex].length === 0) {
            updatedFilters.splice(cardIndex, 1); // Remove card if it has no filters
        }

        // Remove the corresponding filter result as well
        const updatedFilterResults = [...filterResults];
        if (updatedFilterResults[cardIndex]) {
            updatedFilterResults[cardIndex].splice(rowIndex, 1);
            if (updatedFilterResults[cardIndex].length === 0) {
                updatedFilterResults.splice(cardIndex, 1);
            }
        }

        setFilters(updatedFilters);
        setFilterResults(updatedFilterResults);

        // Check if there are no filters left
        if (updatedFilters.length === 0 && updatedFilterResults.length === 0) {
            setFilterMapping(false); // Set filter mapping to false if no filters remain
        }
        // Pass updated filter results to parent component
        onFiltersFinalized(updatedFilters);

    };

    return (
        <div id="whole-filter">
            {mappingLoading ? (
                <div
                    className="spinner-container"
                    style={{
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        height: "70vh", // Full viewport height
                    }}
                >
                    <Spin indicator={<LoadingOutlined style={{ fontSize: 48 }} spin />} /> {/* Ant Design spinner */}
                </div>
            ) : (
                filters.map((filterRows, cardIndex) => (
                    <React.Fragment key={cardIndex}>
                        <Card
                            style={{
                                marginBottom: "10px",

                                border: "1px solid rgba(0, 0, 0, 0.1)",
                            }}
                        >
                            {filterRows.map((filter, rowIndex) => (
                                <div
                                    key={filter.id}
                                    id={`filter-${filter.id}`}
                                    className="d-flex justify-content-between align-items-center"
                                    style={{ marginTop: rowIndex > 0 ? "20px" : "0" }}
                                >
                                    <div className="d-flex flex-column">
                                        <label htmlFor={`attributes-${filter.id}`}>SELECT ATTRIBUTE</label>
                                        <Select
                                            showSearch
                                            loading={loading}
                                            style={{ width: 180 }}
                                            placeholder="All Attributes"
                                            options={attributes}
                                            onChange={(value) => handleChange(value, cardIndex, rowIndex)}
                                            value={filter.selectedAttribute ? JSON.stringify(filter.selectedAttribute) : null}
                                        ></Select>
                                    </div>
                                    <div className="d-flex flex-column">
                                        <label htmlFor={`formula-${filter.id}`}>FORMULA</label>
                                        <Select
                                            style={{ width: 180 }}
                                            placeholder="Select a Formula"
                                            disabled={filter.isFormulaSelectDisabled}
                                            loading={loading}
                                            options={filter.formulaOptions.map((option) => ({
                                                label: option.label,
                                                value: option.value,
                                            }))}
                                            value={filter.selectedFormula}
                                            onChange={(value) => handleFilterChange("formula", value, cardIndex, rowIndex)}
                                        />
                                    </div>
                                    <div>
                                        {/* Handle multi-select attributes */}
                                        {filter.selectedAttribute &&
                                            filter.selectedAttribute.type === "multi_select" &&
                                            filter.selectedFormula &&
                                            filter.selectedFormula !== "is_defined" &&
                                            filter.selectedFormula !== "is_not_defined" && (
                                                <div className="d-flex flex-column">
                                                    <label htmlFor={`Value-${filter.id}`}>Value</label>
                                                    <TreeSelect
                                                        showSearch
                                                        style={{ width: 180 }}
                                                        dropdownStyle={{
                                                            maxHeight: 400,
                                                            overflow: "auto",
                                                        }}
                                                        placeholder="Please select"
                                                        allowClear
                                                        multiple
                                                        treeDefaultExpandAll
                                                        value={filter.selectedTreeValue}
                                                        onChange={(value) => handleFilterChange("treeSelect", value, cardIndex, rowIndex)}
                                                        treeData={filter.treeData}
                                                    />
                                                </div>
                                            )}

                                        {/* Handle short_text or number input */}
                                        {filter.selectedAttribute &&
                                            ["short_text", "number"].includes(filter.selectedAttribute.type) &&
                                            filter.selectedFormula &&
                                            filter.selectedFormula !== "is_defined" &&
                                            filter.selectedFormula !== "is_not_defined" && (
                                                <div className="d-flex flex-column">
                                                    <label htmlFor={`Value-${filter.id}`}>Value</label>
                                                    <Input
                                                        placeholder="Enter value"
                                                        onChange={(e) => handleFilterChange("input", e, cardIndex, rowIndex)}
                                                        value={filter.inputValue}
                                                        style={{ width: 180 }}
                                                    />
                                                </div>
                                            )}

                                        {/* Handle dropdown attributes */}
                                        {filter.selectedAttribute &&
                                            filter.selectedAttribute.type === "dropdown" &&
                                            filter.selectedFormula &&
                                            filter.selectedFormula !== "is_defined" &&
                                            filter.selectedFormula !== "is_not_defined" && (
                                                <div className="d-flex flex-column">
                                                    <label htmlFor={`Value-${filter.id}`}>VALUE</label>
                                                    <Select
                                                        style={{ width: 180 }}
                                                        placeholder="Select an Option"
                                                        options={filter.treeData}
                                                        onChange={(value) => handleFilterChange("dropdown", value, cardIndex, rowIndex)}
                                                        value={filter.selectedDropdownValue}
                                                    />
                                                </div>
                                            )}

                                        {/* Handle long_text attributes */}
                                        {filter.selectedAttribute &&
                                            filter.selectedAttribute.type === "long_text" &&
                                            filter.selectedFormula &&
                                            filter.selectedFormula !== "is_defined" &&
                                            filter.selectedFormula !== "is_not_defined" && (
                                                <div className="d-flex flex-column">
                                                    <label htmlFor={`Value-${filter.id}`}>VALUE</label>
                                                    <TextArea
                                                        placeholder="Enter value"
                                                        onChange={(e) => handleFilterChange("input", e, cardIndex, rowIndex)}
                                                        value={filter.inputValue}
                                                        style={{ width: 180 }}
                                                    />
                                                </div>
                                            )}
                                    </div>

                                    <div className="d-flex align-items-center mt-4">
                                        <DeleteOutlined
                                            onClick={() => handleDeleteFilter(cardIndex, rowIndex)}
                                            style={{
                                                color: "red",
                                                fontSize: 20,
                                            }}
                                        />
                                    </div>
                                </div>
                            ))}
                            <div className="d-flex gap-4 pt-3">
                                <button onClick={() => addNewRow(cardIndex)} className="bg-transparent border-0 text-primary fw-bold">
                                    AND
                                </button>
                                <button onClick={addNewCard} className="bg-transparent border-0 text-primary fw-bold">
                                    OR
                                </button>
                            </div>
                        </Card>
                        {cardIndex < filters.length - 1 && (
                            <div
                                style={{
                                    textAlign: "center",
                                    margin: "10px 0",
                                    fontWeight: "bold",
                                }}
                            >
                                ----- OR -----
                            </div>
                        )}
                    </React.Fragment>
                ))
            )}
        </div>
    );
};

export default FilterMapping;
