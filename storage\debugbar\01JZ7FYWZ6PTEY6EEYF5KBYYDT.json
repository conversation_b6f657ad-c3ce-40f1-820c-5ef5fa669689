{"__meta": {"id": "01JZ7FYWZ6PTEY6EEYF5KBYYDT", "datetime": "2025-07-03 06:35:37", "utime": **********.319138, "method": "GET", "uri": "/products/2/edit/1", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 2, "messages": [{"message": "[06:35:33] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.016061, "xdebug_link": null, "collector": "log"}, {"message": "[06:35:36] LOG.warning: strlen(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Rules\\MaxRule.php on line 49", "message_html": null, "is_string": false, "label": "warning", "time": **********.102692, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.261576, "end": **********.319162, "duration": 5.057585954666138, "duration_str": "5.06s", "measures": [{"label": "Booting", "start": **********.261576, "relative_start": 0, "end": **********.899762, "relative_end": **********.899762, "duration": 0.***************, "duration_str": "638ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.899774, "relative_start": 0.****************, "end": **********.319165, "relative_end": 3.0994415283203125e-06, "duration": 4.***************, "duration_str": "4.42s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.914613, "relative_start": 0.****************, "end": **********.92403, "relative_end": **********.92403, "duration": 0.009417057037353516, "duration_str": "9.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.319257, "relative_start": 1.****************, "end": **********.316484, "relative_end": **********.316484, "duration": 3.****************, "duration_str": "4s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 22, "nb_templates": 22, "templates": [{"name": "1x products.add", "param_count": null, "params": [], "start": **********.324428, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.phpproducts.add", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fproducts%2Fadd.blade.php&line=1", "ajax": false, "filename": "add.blade.php", "line": "?"}, "render_count": 1, "name_original": "products.add"}, {"name": "1x components.products.edit-product-header", "param_count": null, "params": [], "start": 1751524534.796931, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header.blade.phpcomponents.products.edit-product-header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header.blade.php&line=1", "ajax": false, "filename": "edit-product-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-header"}, {"name": "1x completeness::components.product-header", "param_count": null, "params": [], "start": **********.095928, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-header.blade.phpcompleteness::components.product-header", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2Fviews%2Fcomponents%2Fproduct-header.blade.php&line=1", "ajax": false, "filename": "product-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "completeness::components.product-header"}, {"name": "1x completeness::components.product-score-progress-bar", "param_count": null, "params": [], "start": **********.322618, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-score-progress-bar.blade.phpcompleteness::components.product-score-progress-bar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2Fviews%2Fcomponents%2Fproduct-score-progress-bar.blade.php&line=1", "ajax": false, "filename": "product-score-progress-bar.blade.php", "line": "?"}, "render_count": 1, "name_original": "completeness::components.product-score-progress-bar"}, {"name": "1x components.products.edit-product-header-btns", "param_count": null, "params": [], "start": **********.373764, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-btns.blade.phpcomponents.products.edit-product-header-btns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header-btns.blade.php&line=1", "ajax": false, "filename": "edit-product-header-btns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-header-btns"}, {"name": "1x components.products.edit-product-header-navs", "param_count": null, "params": [], "start": **********.568931, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-navs.blade.phpcomponents.products.edit-product-header-navs", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header-navs.blade.php&line=1", "ajax": false, "filename": "edit-product-header-navs.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-header-navs"}, {"name": "6x components.products.edit-product-attribute-status-dot", "param_count": null, "params": [], "start": **********.886971, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-attribute-status-dot.blade.phpcomponents.products.edit-product-attribute-status-dot", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-attribute-status-dot.blade.php&line=1", "ajax": false, "filename": "edit-product-attribute-status-dot.blade.php", "line": "?"}, "render_count": 6, "name_original": "components.products.edit-product-attribute-status-dot"}, {"name": "1x completeness::components.product-fields-scoring", "param_count": null, "params": [], "start": **********.119215, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-fields-scoring.blade.phpcompleteness::components.product-fields-scoring", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2Fviews%2Fcomponents%2Fproduct-fields-scoring.blade.php&line=1", "ajax": false, "filename": "product-fields-scoring.blade.php", "line": "?"}, "render_count": 1, "name_original": "completeness::components.product-fields-scoring"}, {"name": "1x components.products.edit-product-selection-widget", "param_count": null, "params": [], "start": **********.183895, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-selection-widget.blade.phpcomponents.products.edit-product-selection-widget", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-selection-widget.blade.php&line=1", "ajax": false, "filename": "edit-product-selection-widget.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-selection-widget"}, {"name": "1x components.products.edit-product-footer-btns", "param_count": null, "params": [], "start": **********.826601, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-footer-btns.blade.phpcomponents.products.edit-product-footer-btns", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-footer-btns.blade.php&line=1", "ajax": false, "filename": "edit-product-footer-btns.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-footer-btns"}, {"name": "1x components.products.edit-product-base-form", "param_count": null, "params": [], "start": **********.867613, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-base-form.blade.phpcomponents.products.edit-product-base-form", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-base-form.blade.php&line=1", "ajax": false, "filename": "edit-product-base-form.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.edit-product-base-form"}, {"name": "2x components.assets.delete-modal", "param_count": null, "params": [], "start": **********.100534, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/assets/delete-modal.blade.phpcomponents.assets.delete-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fassets%2Fdelete-modal.blade.php&line=1", "ajax": false, "filename": "delete-modal.blade.php", "line": "?"}, "render_count": 2, "name_original": "components.assets.delete-modal"}, {"name": "1x layouts.app_new", "param_count": null, "params": [], "start": **********.105549, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.phplayouts.app_new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=1", "ajax": false, "filename": "app_new.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app_new"}, {"name": "1x layouts.navs.product_sidebar", "param_count": null, "params": [], "start": **********.109516, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.phplayouts.navs.product_sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=1", "ajax": false, "filename": "product_sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navs.product_sidebar"}, {"name": "1x components.alerts.import-alert", "param_count": null, "params": [], "start": **********.300931, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/import-alert.blade.phpcomponents.alerts.import-alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fimport-alert.blade.php&line=1", "ajax": false, "filename": "import-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.import-alert"}, {"name": "1x components.alerts.upgrade-billing-modal", "param_count": null, "params": [], "start": **********.31096, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/upgrade-billing-modal.blade.phpcomponents.alerts.upgrade-billing-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fupgrade-billing-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-billing-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.upgrade-billing-modal"}]}, "route": {"uri": "GET products/{id}/edit/{version_id?}", "middleware": "web, check_session, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ProductController@edit<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "as": "products.edit", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:125-217</a>"}, "queries": {"count": 143, "nb_statements": 143, "nb_visible_statements": 143, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.14025, "accumulated_duration_str": "140ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.948627, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 2.182}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.9616761, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 2.182, "width_percent": 0.471}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.975974, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 2.652, "width_percent": 0.578}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.9872298, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 3.23, "width_percent": 0.535}, {"sql": "select * from `products` where `products`.`id` = '2' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["2", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9999418, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 3.765, "width_percent": 0.406}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.00479, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.171, "width_percent": 0.734}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.010572, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 4.906, "width_percent": 0.485}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at`, `invite_product`.`id` as `pivotId` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0171869, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.39, "width_percent": 0.556}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0229611, "duration": 0.00138, "duration_str": "1.38ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 5.947, "width_percent": 0.984}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.029208, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 6.93, "width_percent": 0.556}, {"sql": "select * from `variants` where `variants`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.035539, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.487, "width_percent": 0.535}, {"sql": "select `files`.*, `file_product`.`product_id` as `pivot_product_id`, `file_product`.`file_id` as `pivot_file_id`, `file_product`.`uploaded_for` as `pivot_uploaded_for`, `file_product`.`created_at` as `pivot_created_at`, `file_product`.`updated_at` as `pivot_updated_at` from `files` inner join `file_product` on `files`.`id` = `file_product`.`file_id` where `file_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.044225, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 8.021, "width_percent": 0.542}, {"sql": "select * from `inventories` where `variant_id` is null and `inventories`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.048482, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:135", "source": {"index": 23, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 135}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=135", "ajax": false, "filename": "ProductController.php", "line": "135"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 8.563, "width_percent": 0.528}, {"sql": "select * from `families` where exists (select * from `attribute_family_product_versions` where `families`.`id` = `attribute_family_product_versions`.`family_id` and `product_id` = 2 and `version_id` = 1) order by `id` asc", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.053824, "duration": 0.00775, "duration_str": "7.75ms", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 9.091, "width_percent": 5.526}, {"sql": "select `attributes`.*, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.069447, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 14.617, "width_percent": 0.542}, {"sql": "select * from `attribute_types` where `attribute_types`.`id` in (1, 3, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.077363, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.159, "width_percent": 0.463}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.081079, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.622, "width_percent": 0.392}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 1 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.084285, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.014, "width_percent": 0.62}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 3 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 3, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0903358, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.635, "width_percent": 0.556}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 4 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 4, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.093897, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.191, "width_percent": 0.62}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 5 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 5, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.0978801, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.811, "width_percent": 0.606}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 6 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 6, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.101739, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.417, "width_percent": 0.62}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 7 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 7, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.1074069, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.037, "width_percent": 0.677}, {"sql": "select * from `versions` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 140}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.11115, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:140", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 140}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=140", "ajax": false, "filename": "ProductController.php", "line": "140"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.715, "width_percent": 0.421}, {"sql": "select * from `versions` where `versions`.`id` = '1' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 153}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.1151419, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:153", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 153}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=153", "ajax": false, "filename": "ProductController.php", "line": "153"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.135, "width_percent": 0.385}, {"sql": "select `id` from `product_version` where `version_id` = 1 and `product_id` = '2'", "type": "query", "params": [], "bindings": [1, "2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.118417, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:23", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=23", "ajax": false, "filename": "VersionScoreTrait.php", "line": "23"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.52, "width_percent": 0.399}, {"sql": "select `id` from `families` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1247969, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:32", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=32", "ajax": false, "filename": "VersionScoreTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 20.92, "width_percent": 0.428}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.128727, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:39", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=39", "ajax": false, "filename": "VersionScoreTrait.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.348, "width_percent": 0.421}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` = 2 and `value` is not null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.1326091, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:43", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=43", "ajax": false, "filename": "VersionScoreTrait.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.768, "width_percent": 0.471}, {"sql": "select count(*) as aggregate from `brand_product` where `product_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.13631, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:45", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=45", "ajax": false, "filename": "VersionScoreTrait.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.239, "width_percent": 0.385}, {"sql": "select count(*) as aggregate from `category_product` where `product_id` = '2' and `category_product`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.244938, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:46", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=46", "ajax": false, "filename": "VersionScoreTrait.php", "line": "46"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 22.624, "width_percent": 0.428}, {"sql": "select count(*) as aggregate from `product_vendor` where `product_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 155}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.249695, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:47", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=47", "ajax": false, "filename": "VersionScoreTrait.php", "line": "47"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 23.052, "width_percent": 0.364}, {"sql": "select `id` from `product_version` where `product_id` = '2' and `version_id` = 1", "type": "query", "params": [], "bindings": ["2", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 168}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.253548, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:168", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 168}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=168", "ajax": false, "filename": "ProductController.php", "line": "168"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 23.415, "width_percent": 0.414}, {"sql": "select * from `product_version` where `product_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1770}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.259512, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:1770", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1770}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1770", "ajax": false, "filename": "Product.php", "line": "1770"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 23.829, "width_percent": 0.364}, {"sql": "select * from `versions` where `versions`.`id` = 1 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1772}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.26287, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Product.php:1772", "source": {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1772}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1772", "ajax": false, "filename": "Product.php", "line": "1772"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.193, "width_percent": 0.364}, {"sql": "select `id` from `product_version` where `product_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 69}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2656581, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:69", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 69}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=69", "ajax": false, "filename": "VersionScoreTrait.php", "line": "69"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.556, "width_percent": 0.342}, {"sql": "select `id` from `families` where `organization_id` = 1 and (`name` = 'General' or `name` = 'SEO')", "type": "query", "params": [], "bindings": [1, "General", "SEO"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.268947, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:81", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=81", "ajax": false, "filename": "VersionScoreTrait.php", "line": "81"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.898, "width_percent": 0.385}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 88}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.275056, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:88", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 88}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=88", "ajax": false, "filename": "VersionScoreTrait.php", "line": "88"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 25.283, "width_percent": 0.406}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` in (2) and `value` is not null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 93}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1773}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2784078, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:93", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 93}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=93", "ajax": false, "filename": "VersionScoreTrait.php", "line": "93"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 25.69, "width_percent": 0.435}, {"sql": "select `id` from `product_version` where `product_id` = '2'", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 99}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.281553, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:99", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 99}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=99", "ajax": false, "filename": "VersionScoreTrait.php", "line": "99"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.125, "width_percent": 0.321}, {"sql": "select `id` from `families` where `organization_id` = 1 and (`name` = 'General' or `name` = 'SEO')", "type": "query", "params": [], "bindings": [1, "General", "SEO"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 111}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.28482, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:111", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 111}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=111", "ajax": false, "filename": "VersionScoreTrait.php", "line": "111"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.446, "width_percent": 0.364}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 118}, {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2902272, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:118", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 118}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=118", "ajax": false, "filename": "VersionScoreTrait.php", "line": "118"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.809, "width_percent": 0.449}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` in (2) and `value` is null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1774}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 184}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.2936358, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:124", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=124", "ajax": false, "filename": "VersionScoreTrait.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.258, "width_percent": 0.399}, {"sql": "select * from `families` where `is_default` = 0 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [0, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 188}, {"index": 16, "namespace": null, "name": "app/Models/Product/Family.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Family.php", "line": 85}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 187}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.296833, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:188", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=188", "ajax": false, "filename": "ProductController.php", "line": "188"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.658, "width_percent": 0.349}, {"sql": "select * from `organizations` where `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 191}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.300405, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:191", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 191}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=191", "ajax": false, "filename": "ProductController.php", "line": "191"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.007, "width_percent": 0.485}, {"sql": "select * from `versions` where `versions`.`id` = '1' and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.304378, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:193", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=193", "ajax": false, "filename": "ProductController.php", "line": "193"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.492, "width_percent": 1.226}, {"sql": "select * from `locations` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 197}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.309955, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:197", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 197}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=197", "ajax": false, "filename": "ProductController.php", "line": "197"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.718, "width_percent": 0.934}, {"sql": "select * from `variants` where `product_id` = '2' and `version_id` = '1'", "type": "query", "params": [], "bindings": ["2", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 199}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.313959, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ProductController.php:199", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 199}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=199", "ajax": false, "filename": "ProductController.php", "line": "199"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 30.652, "width_percent": 0.399}, {"sql": "select * from `product_version` where `product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductScoreProgressBar.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductScoreProgressBar.php", "line": 38}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "completeness::components.product-header", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Providers/../views/components/product-header.blade.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3170161, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ProductScoreProgressBar.php:38", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductScoreProgressBar.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductScoreProgressBar.php", "line": 38}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FComponents%2FProductScoreProgressBar.php&line=38", "ajax": false, "filename": "ProductScoreProgressBar.php", "line": "38"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.052, "width_percent": 0.492}, {"sql": "select `id` from `products` where `id` > 2 and `organization_id` = '1' order by `id` asc limit 1", "type": "query", "params": [], "bindings": [2, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 35}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "components.products.edit-product-header", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.3639972, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "EditProductHeaderBtns.php:35", "source": {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 35}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderBtns.php&line=35", "ajax": false, "filename": "EditProductHeaderBtns.php", "line": "35"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.544, "width_percent": 0.941}, {"sql": "select `id` from `products` where `id` < 2 and `organization_id` = '1' order by `id` desc limit 1", "type": "query", "params": [], "bindings": [2, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "components.products.edit-product-header", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header.blade.php", "line": 28}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.368722, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderBtns.php:36", "source": {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderBtns.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderBtns.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderBtns.php&line=36", "ajax": false, "filename": "EditProductHeaderBtns.php", "line": "36"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.485, "width_percent": 0.549}, {"sql": "select * from `families` where `is_default` = 1 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 40}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.440896, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:40", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=40", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "40"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.034, "width_percent": 1.134}, {"sql": "select * from `versions` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 41}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.445739, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:41", "source": {"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=41", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "41"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.168, "width_percent": 0.399}, {"sql": "select `id` from `product_version` where `version_id` = 1 and `product_id` = 2", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.449302, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:23", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 23}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=23", "ajax": false, "filename": "VersionScoreTrait.php", "line": "23"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.567, "width_percent": 0.406}, {"sql": "select `id` from `families` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.452526, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:32", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=32", "ajax": false, "filename": "VersionScoreTrait.php", "line": "32"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.973, "width_percent": 0.371}, {"sql": "select `id` from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.45711, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:39", "source": {"index": 14, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=39", "ajax": false, "filename": "VersionScoreTrait.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.344, "width_percent": 0.528}, {"sql": "select count(*) as aggregate from `attribute_family_product_versions` where `attribute_family_id` in (1, 2, 3, 4, 5, 6) and `product_version_id` = 2 and `value` is not null", "type": "query", "params": [], "bindings": [1, 2, 3, 4, 5, 6, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4604678, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:43", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=43", "ajax": false, "filename": "VersionScoreTrait.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.872, "width_percent": 0.549}, {"sql": "select count(*) as aggregate from `brand_product` where `product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.4644802, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:45", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=45", "ajax": false, "filename": "VersionScoreTrait.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.421, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from `category_product` where `product_id` = 2 and `category_product`.`deleted_at` is null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.467599, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:46", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 46}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=46", "ajax": false, "filename": "VersionScoreTrait.php", "line": "46"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.884, "width_percent": 0.421}, {"sql": "select count(*) as aggregate from `product_vendor` where `product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 42}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.471519, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:47", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=47", "ajax": false, "filename": "VersionScoreTrait.php", "line": "47"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.305, "width_percent": 0.492}, {"sql": "select * from `product_version` where `product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 43}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4746618, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:43", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=43", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 37.797, "width_percent": 0.421}, {"sql": "select * from `versions` where `versions`.`id` = 1 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 48}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 22, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.4784591, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "EditProductHeaderNavs.php:48", "source": {"index": 20, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductHeaderNavs.php&line=48", "ajax": false, "filename": "EditProductHeaderNavs.php", "line": "48"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.217, "width_percent": 0.592}, {"sql": "select * from `products` where `products`.`id` = 2 and `organization_id` = '1' limit 1", "type": "query", "params": [], "bindings": [2, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 20, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 21, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 22, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.481908, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.809, "width_percent": 0.492}, {"sql": "select `brands`.*, `brand_product`.`product_id` as `pivot_product_id`, `brand_product`.`brand_id` as `pivot_brand_id`, `brand_product`.`created_at` as `pivot_created_at`, `brand_product`.`updated_at` as `pivot_updated_at` from `brands` inner join `brand_product` on `brands`.`id` = `brand_product`.`brand_id` where `brand_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.485483, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.301, "width_percent": 0.492}, {"sql": "select `categories`.*, `category_product`.`product_id` as `pivot_product_id`, `category_product`.`category_id` as `pivot_category_id`, `category_product`.`created_at` as `pivot_created_at`, `category_product`.`updated_at` as `pivot_updated_at` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.490428, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.793, "width_percent": 0.535}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at`, `invite_product`.`id` as `pivotId` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.494426, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.328, "width_percent": 0.699}, {"sql": "select `versions`.*, `product_version`.`product_id` as `pivot_product_id`, `product_version`.`version_id` as `pivot_version_id`, `product_version`.`id` as `pivotId` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.498048, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.027, "width_percent": 0.528}, {"sql": "select `channels`.*, `channel_product`.`product_id` as `pivot_product_id`, `channel_product`.`channel_id` as `pivot_channel_id`, `channel_product`.`id` as `pivot_id`, `channel_product`.`created_at` as `pivot_created_at`, `channel_product`.`updated_at` as `pivot_updated_at` from `channels` inner join `channel_product` on `channels`.`id` = `channel_product`.`channel_id` where `channel_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.501831, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.554, "width_percent": 0.578}, {"sql": "select * from `variants` where `variants`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 27, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.507149, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.132, "width_percent": 0.649}, {"sql": "select `files`.*, `file_product`.`product_id` as `pivot_product_id`, `file_product`.`file_id` as `pivot_file_id`, `file_product`.`uploaded_for` as `pivot_uploaded_for`, `file_product`.`created_at` as `pivot_created_at`, `file_product`.`updated_at` as `pivot_updated_at` from `files` inner join `file_product` on `files`.`id` = `file_product`.`file_id` where `file_product`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 23, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 24, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 26, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.511547, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.781, "width_percent": 0.578}, {"sql": "select * from `inventories` where `variant_id` is null and `inventories`.`product_id` in (2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1090}, {"index": 25, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 27, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.51505, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:236", "source": {"index": 23, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 236}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=236", "ajax": false, "filename": "VersionScoreTrait.php", "line": "236"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.358, "width_percent": 0.485}, {"sql": "select * from `families` where exists (select * from `attribute_family_product_versions` where `families`.`id` = `attribute_family_product_versions`.`family_id` and `product_id` = 2 and `version_id` = 1) order by `id` asc", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.5185409, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.843, "width_percent": 0.677}, {"sql": "select `attributes`.*, `attribute_family`.`family_id` as `pivot_family_id`, `attribute_family`.`attribute_id` as `pivot_attribute_id`, `attribute_family`.`id` as `pivotId` from `attributes` inner join `attribute_family` on `attributes`.`id` = `attribute_family`.`attribute_id` where `attribute_family`.`family_id` in (1, 2)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 20, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 21, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 22, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 23, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.523295, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 19, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 44.52, "width_percent": 0.613}, {"sql": "select * from `attribute_types` where `attribute_types`.`id` in (1, 3, 11)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 27, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 28, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.52832, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.134, "width_percent": 0.52}, {"sql": "select * from `attribute_options` where `attribute_options`.`attribute_id` in (1, 3, 4, 5, 6, 7)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, {"index": 25, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 26, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 27, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 28, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.53159, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Product.php:1013", "source": {"index": 24, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1013}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1013", "ajax": false, "filename": "Product.php", "line": "1013"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.654, "width_percent": 0.471}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 1 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 1, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.5352662, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 46.125, "width_percent": 0.727}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 1 and `attribute_id` = 3 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [1, 3, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.5397458, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 46.852, "width_percent": 0.549}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 4 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 4, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.543964, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.401, "width_percent": 0.742}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 5 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 5, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.547713, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 48.143, "width_percent": 0.699}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 6 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 6, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.551821, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 48.841, "width_percent": 0.72}, {"sql": "select `id`, `value`, `unit` from `attribute_family_product_versions` where `family_id` = 2 and `attribute_id` = 7 and `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 7, 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, {"index": 16, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1098}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 235}, {"index": 18, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 19, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}], "start": **********.55719, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "Product.php:1023", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Product.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Product.php", "line": 1023}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1023", "ajax": false, "filename": "Product.php", "line": "1023"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 49.561, "width_percent": 0.749}, {"sql": "select count(*) as aggregate from `brand_product` where `product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 401}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}], "start": **********.561991, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:401", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 401}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=401", "ajax": false, "filename": "VersionScoreTrait.php", "line": "401"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.31, "width_percent": 0.463}, {"sql": "select count(*) as aggregate from `category_product` where `product_id` = 2 and `category_product`.`deleted_at` is null", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 402}, {"index": 17, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 412}, {"index": 18, "namespace": null, "name": "app/View/Components/Products/EditProductHeaderNavs.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductHeaderNavs.php", "line": 49}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 53}], "start": **********.5652351, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "VersionScoreTrait.php:402", "source": {"index": 16, "namespace": null, "name": "app/Traits/Version/VersionScoreTrait.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Traits\\Version\\VersionScoreTrait.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FTraits%2FVersion%2FVersionScoreTrait.php&line=402", "ajax": false, "filename": "VersionScoreTrait.php", "line": "402"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.774, "width_percent": 0.471}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.872951, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 51.244, "width_percent": 0.891}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.878185, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.135, "width_percent": 0.385}, {"sql": "select `versions`.`id` from `versions` inner join `product_version` on `versions`.`id` = `product_version`.`version_id` where `product_version`.`product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "view", "name": "components.products.edit-product-header-navs", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-navs.blade.php", "line": 64}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.882642, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "components.products.edit-product-header-navs:64", "source": {"index": 17, "namespace": "view", "name": "components.products.edit-product-header-navs", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/edit-product-header-navs.blade.php", "line": 64}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fedit-product-header-navs.blade.php&line=64", "ajax": false, "filename": "edit-product-header-navs.blade.php", "line": "64"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.52, "width_percent": 0.385}, {"sql": "select `id` from `families` where `is_default` = 1 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 301}, {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 25}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.964401, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:301", "source": {"index": 14, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 301}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=301", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "301"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.906, "width_percent": 0.463}, {"sql": "select `family_id` from `attribute_family_product_versions` where `attribute_family_product_versions`.`product_id` = 2 and `attribute_family_product_versions`.`product_id` is not null and `family_id` not in (1, 2)", "type": "query", "params": [], "bindings": [2, 1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 303}, {"index": 18, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 25}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 20, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.968054, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:303", "source": {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 303}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=303", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "303"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.369, "width_percent": 0.499}, {"sql": "(select `attribute_id`, `value`, `attributes`.`rules` from `attribute_family_product_versions` left join `attributes` on `attribute_family_product_versions`.`attribute_id` = `attributes`.`id` where `attribute_family_product_versions`.`product_id` = 2 and `attribute_family_product_versions`.`product_id` is not null and `version_id` = 1 and `attributes`.`attribute_type_id` <> 13) union (select id as attribute_id ,null as value, `rules` from `attributes` where `attribute_type_id` <> 13 and exists (select * from `families` inner join `attribute_family` on `families`.`id` = `attribute_family`.`family_id` where `attributes`.`id` = `attribute_family`.`attribute_id` and (`is_default` = 1 or 0 = 1)) and `organization_id` = '1')", "type": "query", "params": [], "bindings": [2, 1, 13, 13, 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 316}, {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.973962, "duration": 0.01952, "duration_str": "19.52ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:316", "source": {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 316}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=316", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "316"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 53.868, "width_percent": 13.918}, {"sql": "select * from `variants` where `product_id` = 2 and `version_id` = 1", "type": "query", "params": [], "bindings": [2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 163}, {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.103785, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:163", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 163}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=163", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "163"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 67.786, "width_percent": 0.963}, {"sql": "select * from `settings` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 169}, {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 30}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}], "start": **********.108198, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:63", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=63", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "63"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 68.749, "width_percent": 0.513}, {"sql": "select `invites`.*, `invite_product`.`product_id` as `pivot_product_id`, `invite_product`.`invite_id` as `pivot_invite_id`, `invite_product`.`created_at` as `pivot_created_at`, `invite_product`.`updated_at` as `pivot_updated_at` from `invites` inner join `invite_product` on `invites`.`id` = `invite_product`.`invite_id` where `invite_product`.`product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 115}, {"index": 21, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 90}, {"index": 22, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 35}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}, {"index": 24, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 356}], "start": **********.111955, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:115", "source": {"index": 20, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 115}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=115", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "115"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.262, "width_percent": 0.542}, {"sql": "select * from `settings` where `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, {"index": 16, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 118}, {"index": 17, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 90}, {"index": 18, "namespace": null, "name": "packages/apimio/completeness/src/Components/ProductFieldsScoring.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Components\\ProductFieldsScoring.php", "line": 35}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 102}], "start": **********.115163, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "CalculateScoreHandler.php:63", "source": {"index": 15, "namespace": null, "name": "packages/apimio/completeness/src/Handlers/CalculateScoreHandler.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\completeness\\src\\Handlers\\CalculateScoreHandler.php", "line": 63}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fcompleteness%2Fsrc%2FHandlers%2FCalculateScoreHandler.php&line=63", "ajax": false, "filename": "CalculateScoreHandler.php", "line": "63"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.804, "width_percent": 0.492}, {"sql": "select * from `brands` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 37}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.152116, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:37", "source": {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 37}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=37", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "37"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 70.296, "width_percent": 0.884}, {"sql": "select * from `invites` where ((`organization_id_sender` != '1' and `email` = '<EMAIL>' and `type` = 'retailer') or (`organization_id_sender` = '1' and (`email` != '<EMAIL>' or `email` is null) and `type` = 'vendor'))", "type": "query", "params": [], "bindings": ["1", "<EMAIL>", "retailer", "1", "<EMAIL>", "vendor"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 39}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 17, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.15868, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:39", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=39", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.18, "width_percent": 0.52}, {"sql": "select * from `categories` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.164721, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:41", "source": {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 41}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=41", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "41"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.701, "width_percent": 0.856}, {"sql": "select * from `channels` where `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1685722, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:43", "source": {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=43", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.556, "width_percent": 1.205}, {"sql": "select `categories`.`id` from `categories` inner join `category_product` on `categories`.`id` = `category_product`.`category_id` where `category_product`.`product_id` = 2", "type": "query", "params": [], "bindings": [2], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 19, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.1738422, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:45", "source": {"index": 17, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=45", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 73.761, "width_percent": 0.449}, {"sql": "select * from `categories` where `category_id` is null and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Product/Category.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Category.php", "line": 259}, {"index": 16, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 18, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.1768548, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "Category.php:259", "source": {"index": 15, "namespace": null, "name": "app/Models/Product/Category.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Product\\Category.php", "line": 259}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FCategory.php&line=259", "ajax": false, "filename": "Category.php", "line": "259"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.21, "width_percent": 0.677}, {"sql": "select `value`, `key` from `settings` where `organization_id` = 1 and `key` in ('vendor', 'category', 'brand')", "type": "query", "params": [], "bindings": [1, "vendor", "category", "brand"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 51}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Component.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Component.php", "line": 135}, {"index": 16, "namespace": "view", "name": "products.add", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/add.blade.php", "line": 379}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.180535, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "EditProductSelectionWidget.php:51", "source": {"index": 14, "namespace": null, "name": "app/View/Components/Products/EditProductSelectionWidget.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\EditProductSelectionWidget.php", "line": 51}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FEditProductSelectionWidget.php&line=51", "ajax": false, "filename": "EditProductSelectionWidget.php", "line": "51"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.888, "width_percent": 0.513}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.111599, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.401, "width_percent": 0.563}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.11649, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.964, "width_percent": 0.421}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.123269, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.385, "width_percent": 0.592}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.1272001, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.977, "width_percent": 0.456}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.131483, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.433, "width_percent": 0.599}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.135154, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.032, "width_percent": 0.463}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.1402872, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.496, "width_percent": 0.592}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.144003, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.087, "width_percent": 0.506}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.1483362, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.594, "width_percent": 0.585}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.151947, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.178, "width_percent": 0.449}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.1589282, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.627, "width_percent": 0.706}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.165391, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.333, "width_percent": 0.478}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.173732, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.811, "width_percent": 0.663}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.178026, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.474, "width_percent": 0.535}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.182364, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.009, "width_percent": 0.592}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.1865609, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.601, "width_percent": 0.535}, {"sql": "select `id` from `organization_user` where (`user_id` = 1 and `organization_id` = '1') limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 65}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.192038, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:57", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=57", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.135, "width_percent": 0.506}, {"sql": "select `id` from `permissions` where `handle` = 'add_and_edit_product' limit 1", "type": "query", "params": [], "bindings": ["add_and_edit_product"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.196056, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:61", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=61", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "61"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.642, "width_percent": 1.127}, {"sql": "select * from `organization_user_permissions` where (`organization_user_id` = 1 and `permission_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}], "start": **********.201285, "duration": 0.00271, "duration_str": "2.71ms", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:71", "source": {"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=71", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "71"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.768, "width_percent": 1.932}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.207583, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.701, "width_percent": 0.592}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.211315, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.292, "width_percent": 0.428}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.215444, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.72, "width_percent": 0.585}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.219399, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.305, "width_percent": 0.649}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.225554, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.954, "width_percent": 0.699}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.2294931, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.652, "width_percent": 0.456}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.2340019, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.109, "width_percent": 0.52}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.238689, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.629, "width_percent": 0.421}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.24291, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.05, "width_percent": 0.52}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.24647, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.57, "width_percent": 0.406}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.252223, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.977, "width_percent": 0.642}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.257221, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 93.619, "width_percent": 0.421}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.2616942, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.039, "width_percent": 0.578}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.2670422, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.617, "width_percent": 0.463}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.272353, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.08, "width_percent": 0.542}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.275969, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.622, "width_percent": 0.421}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.280303, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.043, "width_percent": 0.556}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.283825, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.599, "width_percent": 0.414}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.288569, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.012, "width_percent": 0.542}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.292134, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.554, "width_percent": 0.392}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.296555, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "layouts.navs.product_sidebar:435", "source": {"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=435", "ajax": false, "filename": "product_sidebar.blade.php", "line": "435"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.947, "width_percent": 0.513}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, "1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.302351, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:249", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=249", "ajax": false, "filename": "app_new.blade.php", "line": "249"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.46, "width_percent": 0.67}, {"sql": "select * from `versions` where `versions`.`organization_id` = 1 and `versions`.`organization_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.306894, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:251", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=251", "ajax": false, "filename": "app_new.blade.php", "line": "251"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.13, "width_percent": 0.478}, {"sql": "select count(*) as aggregate from `products` where `organization_id` = '1' and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, {"index": 17, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 360}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.312921, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "User.php:193", "source": {"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=193", "ajax": false, "filename": "User.php", "line": "193"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.608, "width_percent": 0.392}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 23, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Product\\AttributeFamilyProductVersion": {"value": 21, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamilyProductVersion.php&line=1", "ajax": false, "filename": "AttributeFamilyProductVersion.php", "line": "?"}}, "App\\Models\\Setting": {"value": 20, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 16, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 9, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}, "App\\Models\\Product\\ProductVersion": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProductVersion.php&line=1", "ajax": false, "filename": "ProductVersion.php", "line": "?"}}, "App\\Models\\Product\\AttributeType": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeType.php&line=1", "ajax": false, "filename": "AttributeType.php", "line": "?"}}, "App\\Models\\Product\\Variant": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVariant.php&line=1", "ajax": false, "filename": "Variant.php", "line": "?"}}, "App\\Models\\Product\\Product": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\Channel\\Channel": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Location\\Location": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FLocation%2FLocation.php&line=1", "ajax": false, "filename": "Location.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUserPermission": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUserPermission.php&line=1", "ajax": false, "filename": "OrganizationUserPermission.php", "line": "?"}}}, "count": 130, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 21, "messages": [{"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-30227100 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-30227100\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.9939, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => inventory,\n  result => true,\n  user => 1,\n  arguments => [0 => inventory]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1310186878 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess inventory</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"9 characters\">inventory</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[0 =&gt; inventory]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1310186878\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.881442, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1623468739 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1623468739\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.121847, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-905360951 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-905360951\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.130562, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-217132491 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-217132491\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.139085, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => invite-team,\n  result => true,\n  user => 1,\n  arguments => [0 => invite-team]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1013589571 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess invite-team</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"11 characters\">invite-team</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"18 characters\">[0 =&gt; invite-team]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1013589571\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.147381, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => notification,\n  result => true,\n  user => 1,\n  arguments => [0 => notification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1097808294 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess notification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"12 characters\">notification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"19 characters\">[0 =&gt; notification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1097808294\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.156998, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => settings,\n  result => true,\n  user => 1,\n  arguments => [0 => settings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-46751597 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; settings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-46751597\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.172182, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-264713032 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-264713032\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.181428, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => import,\n  result => true,\n  user => 1,\n  arguments => [0 => import]\n]", "message_html": "<pre class=sf-dump id=sf-dump-885972642 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess import</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; import]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-885972642\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.190534, "xdebug_link": null}, {"message": "[\n  ability => add_and_edit_product,\n  target => App\\Models\\Organization\\OrganizationUserPermission,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Organization\\OrganizationUserPermission, 1 => 1]\n]", "message_html": "<pre class=sf-dump id=sf-dump-354713730 data-indent-pad=\"  \"><span class=sf-dump-note>add_and_edit_product App\\Models\\Organization\\OrganizationUserPermission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">add_and_edit_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"50 characters\">App\\Models\\Organization\\OrganizationUserPermission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"65 characters\">[0 =&gt; App\\Models\\Organization\\OrganizationUserPermission, 1 =&gt; 1]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-354713730\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.206658, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => export,\n  result => true,\n  user => 1,\n  arguments => [0 => export]\n]", "message_html": "<pre class=sf-dump id=sf-dump-272088455 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess export</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; export]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-272088455\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.21459, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => category,\n  result => true,\n  user => 1,\n  arguments => [0 => category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-324044281 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-324044281\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.223752, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute-set,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute-set]\n]", "message_html": "<pre class=sf-dump id=sf-dump-995051562 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute-set</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"13 characters\">attribute-set</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[0 =&gt; attribute-set]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-995051562\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.23311, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute]\n]", "message_html": "<pre class=sf-dump id=sf-dump-431719286 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"9 characters\">attribute</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[0 =&gt; attribute]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-431719286\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.241972, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => variant-option,\n  result => true,\n  user => 1,\n  arguments => [0 => variant-option]\n]", "message_html": "<pre class=sf-dump id=sf-dump-572254172 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess variant-option</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"14 characters\">variant-option</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"21 characters\">[0 =&gt; variant-option]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-572254172\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.250991, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => brand,\n  result => true,\n  user => 1,\n  arguments => [0 => brand]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1785288256 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess brand</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">brand</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; brand]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1785288256\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.260539, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => vendor,\n  result => true,\n  user => 1,\n  arguments => [0 => vendor]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1819260871 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess vendor</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">vendor</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; vendor]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1819260871\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.271051, "xdebug_link": null}, {"message": "[\n  ability => create-lang,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => [0 => Object(Illuminate\\Database\\Eloquent\\Builder)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-814735550 data-indent-pad=\"  \"><span class=sf-dump-note>create-lang </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-lang</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"51 characters\">[0 =&gt; Object(Illuminate\\Database\\Eloquent\\Builder)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-814735550\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.279394, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => location,\n  result => true,\n  user => 1,\n  arguments => [0 => location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1964049257 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1964049257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.287523, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => store,\n  result => true,\n  user => 1,\n  arguments => [0 => store]\n]", "message_html": "<pre class=sf-dump id=sf-dump-856240257 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess store</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; store]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-856240257\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.295728, "xdebug_link": null}]}, "session": {"_token": "9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/products/2/edit\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "array:2 [\n  \"01JZ7FYPCEKZ4XE0508XY9SKYJ\" => null\n  \"01JZ7FYQKMAJHCJKNSGH0JQABM\" => null\n]", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:49 [\n          \"Handle\" => \"Handle\"\n          \"Title\" => \"Title\"\n          \"Body (HTML)\" => \"Body (HTML)\"\n          \"Vendor\" => \"Vendor\"\n          \"Type\" => \"Type\"\n          \"Tags\" => \"Tags\"\n          \"Published\" => \"Published\"\n          \"Option1 Name\" => \"Option1 Name\"\n          \"Option1 Value\" => \"Option1 Value\"\n          \"Option2 Name\" => \"Option2 Name\"\n          \"Option2 Value\" => \"Option2 Value\"\n          \"Option3 Name\" => \"Option3 Name\"\n          \"Option3 Value\" => \"Option3 Value\"\n          \"Variant SKU\" => \"Variant SKU\"\n          \"Variant Grams\" => \"Variant Grams\"\n          \"Variant Inventory Tracker\" => \"Variant Inventory Tracker\"\n          \"Variant Inventory Qty\" => \"Variant Inventory Qty\"\n          \"Variant Inventory Policy\" => \"Variant Inventory Policy\"\n          \"Variant Fulfillment Service\" => \"Variant Fulfillment Service\"\n          \"Variant Price\" => \"Variant Price\"\n          \"Variant Compare At Price\" => \"Variant Compare At Price\"\n          \"Variant Requires Shipping\" => \"Variant Requires Shipping\"\n          \"Variant Taxable\" => \"Variant Taxable\"\n          \"Variant Barcode\" => \"Variant Barcode\"\n          \"Image Src\" => \"Image Src\"\n          \"Image Position\" => \"Image Position\"\n          \"Image Alt Text\" => \"Image Alt Text\"\n          \"Gift Card\" => \"Gift Card\"\n          \"SEO Title\" => \"SEO Title\"\n          \"SEO Description\" => \"SEO Description\"\n          \"Google Shopping / Google Product Category\" => \"Google Shopping / Google Product Category\"\n          \"Google Shopping / Gender\" => \"Google Shopping / Gender\"\n          \"Google Shopping / Age Group\" => \"Google Shopping / Age Group\"\n          \"Google Shopping / MPN\" => \"Google Shopping / MPN\"\n          \"Google Shopping / AdWords Grouping\" => \"Google Shopping / AdWords Grouping\"\n          \"Google Shopping / AdWords Labels\" => \"Google Shopping / AdWords Labels\"\n          \"Google Shopping / Condition\" => \"Google Shopping / Condition\"\n          \"Google Shopping / Custom Product\" => \"Google Shopping / Custom Product\"\n          \"Google Shopping / Custom Label 0\" => \"Google Shopping / Custom Label 0\"\n          \"Google Shopping / Custom Label 1\" => \"Google Shopping / Custom Label 1\"\n          \"Google Shopping / Custom Label 2\" => \"Google Shopping / Custom Label 2\"\n          \"Google Shopping / Custom Label 3\" => \"Google Shopping / Custom Label 3\"\n          \"Google Shopping / Custom Label 4\" => \"Google Shopping / Custom Label 4\"\n          \"Variant Image\" => \"Variant Image\"\n          \"Variant Weight Unit\" => \"Variant Weight Unit\"\n          \"Variant Tax Code\" => \"Variant Tax Code\"\n          \"Cost per item\" => \"Cost per item\"\n          \"Status\" => \"Status\"\n          \"List\" => \"List\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:49 [\n        0 => array:2 [\n          \"label\" => \"Handle\"\n          \"value\" => \"Default,Handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Title\"\n          \"value\" => \"Default,Title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Body (HTML)\"\n          \"value\" => \"Default,Body (HTML)\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,Vendor\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Type\"\n          \"value\" => \"Default,Type\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"Default,Tags\"\n        ]\n        6 => array:2 [\n          \"label\" => \"Published\"\n          \"value\" => \"Default,Published\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Option1 Name\"\n          \"value\" => \"Default,Option1 Name\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Option1 Value\"\n          \"value\" => \"Default,Option1 Value\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Option2 Name\"\n          \"value\" => \"Default,Option2 Name\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Option2 Value\"\n          \"value\" => \"Default,Option2 Value\"\n        ]\n        11 => array:2 [\n          \"label\" => \"Option3 Name\"\n          \"value\" => \"Default,Option3 Name\"\n        ]\n        12 => array:2 [\n          \"label\" => \"Option3 Value\"\n          \"value\" => \"Default,Option3 Value\"\n        ]\n        13 => array:2 [\n          \"label\" => \"Variant SKU\"\n          \"value\" => \"Default,Variant SKU\"\n        ]\n        14 => array:2 [\n          \"label\" => \"Variant Grams\"\n          \"value\" => \"Default,Variant Grams\"\n        ]\n        15 => array:2 [\n          \"label\" => \"Variant Inventory Tracker\"\n          \"value\" => \"Default,Variant Inventory Tracker\"\n        ]\n        16 => array:2 [\n          \"label\" => \"Variant Inventory Qty\"\n          \"value\" => \"Default,Variant Inventory Qty\"\n        ]\n        17 => array:2 [\n          \"label\" => \"Variant Inventory Policy\"\n          \"value\" => \"Default,Variant Inventory Policy\"\n        ]\n        18 => array:2 [\n          \"label\" => \"Variant Fulfillment Service\"\n          \"value\" => \"Default,Variant Fulfillment Service\"\n        ]\n        19 => array:2 [\n          \"label\" => \"Variant Price\"\n          \"value\" => \"Default,Variant Price\"\n        ]\n        20 => array:2 [\n          \"label\" => \"Variant Compare At Price\"\n          \"value\" => \"Default,Variant Compare At Price\"\n        ]\n        21 => array:2 [\n          \"label\" => \"Variant Requires Shipping\"\n          \"value\" => \"Default,Variant Requires Shipping\"\n        ]\n        22 => array:2 [\n          \"label\" => \"Variant Taxable\"\n          \"value\" => \"Default,Variant Taxable\"\n        ]\n        23 => array:2 [\n          \"label\" => \"Variant Barcode\"\n          \"value\" => \"Default,Variant Barcode\"\n        ]\n        24 => array:2 [\n          \"label\" => \"Image Src\"\n          \"value\" => \"Default,Image Src\"\n        ]\n        25 => array:2 [\n          \"label\" => \"Image Position\"\n          \"value\" => \"Default,Image Position\"\n        ]\n        26 => array:2 [\n          \"label\" => \"Image Alt Text\"\n          \"value\" => \"Default,Image Alt Text\"\n        ]\n        27 => array:2 [\n          \"label\" => \"Gift Card\"\n          \"value\" => \"Default,Gift Card\"\n        ]\n        28 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"Default,SEO Title\"\n        ]\n        29 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"Default,SEO Description\"\n        ]\n        30 => array:2 [\n          \"label\" => \"Google Shopping / Google Product Category\"\n          \"value\" => \"Default,Google Shopping / Google Product Category\"\n        ]\n        31 => array:2 [\n          \"label\" => \"Google Shopping / Gender\"\n          \"value\" => \"Default,Google Shopping / Gender\"\n        ]\n        32 => array:2 [\n          \"label\" => \"Google Shopping / Age Group\"\n          \"value\" => \"Default,Google Shopping / Age Group\"\n        ]\n        33 => array:2 [\n          \"label\" => \"Google Shopping / MPN\"\n          \"value\" => \"Default,Google Shopping / MPN\"\n        ]\n        34 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Grouping\"\n          \"value\" => \"Default,Google Shopping / AdWords Grouping\"\n        ]\n        35 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Labels\"\n          \"value\" => \"Default,Google Shopping / AdWords Labels\"\n        ]\n        36 => array:2 [\n          \"label\" => \"Google Shopping / Condition\"\n          \"value\" => \"Default,Google Shopping / Condition\"\n        ]\n        37 => array:2 [\n          \"label\" => \"Google Shopping / Custom Product\"\n          \"value\" => \"Default,Google Shopping / Custom Product\"\n        ]\n        38 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 0\"\n          \"value\" => \"Default,Google Shopping / Custom Label 0\"\n        ]\n        39 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 1\"\n          \"value\" => \"Default,Google Shopping / Custom Label 1\"\n        ]\n        40 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 2\"\n          \"value\" => \"Default,Google Shopping / Custom Label 2\"\n        ]\n        41 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 3\"\n          \"value\" => \"Default,Google Shopping / Custom Label 3\"\n        ]\n        42 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 4\"\n          \"value\" => \"Default,Google Shopping / Custom Label 4\"\n        ]\n        43 => array:2 [\n          \"label\" => \"Variant Image\"\n          \"value\" => \"Default,Variant Image\"\n        ]\n        44 => array:2 [\n          \"label\" => \"Variant Weight Unit\"\n          \"value\" => \"Default,Variant Weight Unit\"\n        ]\n        45 => array:2 [\n          \"label\" => \"Variant Tax Code\"\n          \"value\" => \"Default,Variant Tax Code\"\n        ]\n        46 => array:2 [\n          \"label\" => \"Cost per item\"\n          \"value\" => \"Default,Cost per item\"\n        ]\n        47 => array:2 [\n          \"label\" => \"Status\"\n          \"value\" => \"Default,Status\"\n        ]\n        48 => array:2 [\n          \"label\" => \"List\"\n          \"value\" => \"Default,List\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"data_required\" => array:8 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"1\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n  ]\n  \"import_action\" => \"3\"\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,1\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:49 [\n    0 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n    ]\n    1 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    2 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    3 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n    ]\n    4 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    5 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n    ]\n    6 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    7 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    8 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    9 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    10 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    11 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    12 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    13 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    14 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    15 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    16 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    17 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    18 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    19 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    20 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    21 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    22 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    23 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    24 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    25 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    26 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    27 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    28 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n    ]\n    29 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n    ]\n    30 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    31 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    32 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    33 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    34 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    35 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    36 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    37 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    38 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    39 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    40 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 2\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    41 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 3\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    42 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 4\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    43 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Image\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    44 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Weight Unit\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    45 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Tax Code\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    46 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Cost per item\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    47 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Status\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    48 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,List\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n  ]\n]", "bulk_edit_data": "array:3 [\n  \"target_page\" => 1\n  \"version_id\" => \"1\"\n  \"productIds\" => array:1 [\n    0 => \"1\"\n  ]\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/products/2/edit/1", "action_name": "products.edit", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@edit", "uri": "GET products/{id}/edit/{version_id?}", "controller": "App\\Http\\Controllers\\Product\\ProductController@edit<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=125\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:125-217</a>", "middleware": "web, check_session", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4cdbc9-5fab-4d4a-ab07-8f663857eee6\" target=\"_blank\">View in Telescope</a>", "duration": "5.1s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-241987746 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-241987746\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-777569102 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-777569102\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2009995459 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/products?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkhHdzF6SDlDMHRxR1pZaTRTRi9hN2c9PSIsInZhbHVlIjoiTjNkelVTVVlPTjRwSTVrZkx4ZkhqMW53TFQ0SHE4MjJKQ0FBNUwvdDJ4c3o3NUhoYTYwU0plalhHM1N3UTlHWk4wd2d6cERBVndNVTNRYUxRa0plMkE2QUN3VlAzSGxoNWZoRW56bjZmcnViQ0ZYRW5PdFgzbXZRMEpQT0JkdkgiLCJtYWMiOiI0MjI1NWYwNTUyZGMyMTg2YjJmZjRkOGZlZTU2ODI1MmMyNDhiZDE3YTEyNzhkNGRlYmZhYTJmNzZlYjM2MWFjIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IkhkZk5uUVlZR0hoUzJOdlpIWUlhSmc9PSIsInZhbHVlIjoiZHRpcUR4SVRNVjdWcjlidyt0M0FUbHltdUJZZU05emVJQjFwWGN5RFRwUFoxMUU5K2VKSjJiKzkwVGtGLzNtUXk2QzZyaXBHK2lLWTlzMEpUVnBRcVYzck9uRHdlZFo1UEc0OXRJcU0vbFdrYWpTbmNJQVF4NUltUkZVYmxMQVYiLCJtYWMiOiJmZmM0OGFlZmIyMjA3YjA5MGVmNGE3MzZmYzI0NTIwNTFiNDJmODc3MTI5OGMyNzI3NjhjNWNiNDRiYzhhM2JjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2009995459\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1781432634 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hpj02iYobWSFRbp0Zjz10UyQzMp42LbVgb86a16Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1781432634\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1323947281 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:35:33 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323947281\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-241354127 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/products/2/edit</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ7FYPCEKZ4XE0508XY9SKYJ</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>01JZ7FYQKMAJHCJKNSGH0JQABM</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Handle</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>Body (HTML)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>Vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>Published</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>Option1 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>Option1 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>Option2 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>Option2 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>Option3 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>Option3 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>Variant SKU</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>Variant Grams</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Tracker</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Qty</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>Variant Fulfillment Service</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>Variant Price</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>Variant Compare At Price</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>Variant Requires Shipping</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>Variant Taxable</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>Variant Barcode</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>Image Src</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>Image Position</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>Image Alt Text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>Gift Card</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>SEO Title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>SEO Description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Google Product Category</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Gender</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Age Group</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>Google Shopping / MPN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Grouping</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Labels</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Condition</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Product</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 0</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 1</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 2</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 3</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 4</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>Variant Image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>Variant Weight Unit</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>Variant Tax Code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>Cost per item</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n          </samp>]\n          <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n          </samp>]\n          <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n          </samp>]\n          <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n          </samp>]\n          <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n          </samp>]\n          <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n          </samp>]\n          <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n          </samp>]\n          <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n          </samp>]\n          <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n          </samp>]\n          <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n          </samp>]\n          <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n          </samp>]\n          <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n          </samp>]\n          <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n          </samp>]\n          <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n          </samp>]\n          <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n          </samp>]\n          <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n          </samp>]\n          <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n          </samp>]\n          <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n          </samp>]\n          <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n          </samp>]\n          <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n          </samp>]\n          <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n          </samp>]\n          <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n          </samp>]\n          <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n          </samp>]\n          <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n          </samp>]\n          <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>bulk_edit_data</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>target_page</span>\" => <span class=sf-dump-num>1</span>\n    \"<span class=sf-dump-key>version_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n    \"<span class=sf-dump-key>productIds</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-241354127\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/products/2/edit/1", "action_name": "products.edit", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@edit"}, "badge": null}}