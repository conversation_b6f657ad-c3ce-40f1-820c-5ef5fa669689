{"__meta": {"id": "01JZ51SGQWYZJ520YA9GG2AS7Z", "datetime": "2025-07-02 07:49:32", "utime": **********.030895, "method": "POST", "uri": "/api/2024-12/organization", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[07:49:23] LOG.warning: Optional parameter $invite_channel_ids declared before required parameter $authuser is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php on line 372", "message_html": null, "is_string": false, "label": "warning", "time": **********.752425, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1751442562.552686, "end": **********.030931, "duration": 9.47824501991272, "duration_str": "9.48s", "measures": [{"label": "Booting", "start": 1751442562.552686, "relative_start": 0, "end": **********.10389, "relative_end": **********.10389, "duration": 0.****************, "duration_str": "551ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.103917, "relative_start": 0.****************, "end": **********.030935, "relative_end": 4.0531158447265625e-06, "duration": 8.***************, "duration_str": "8.93s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.119585, "relative_start": 0.****************, "end": **********.125059, "relative_end": **********.125059, "duration": 0.0054738521575927734, "duration_str": "5.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.0205, "relative_start": 9.***************, "end": **********.02126, "relative_end": **********.02126, "duration": 0.0007600784301757812, "duration_str": "760μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.024742, "relative_start": 9.***************, "end": **********.024895, "relative_end": **********.024895, "duration": 0.00015306472778320312, "duration_str": "153μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST api/2024-12/organization", "middleware": "api, auth:sanctum", "as": "organization.store", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>"}, "queries": {"count": 58, "nb_statements": 56, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.12135000000000001, "accumulated_duration_str": "121ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.150505, "duration": 0.01642, "duration_str": "16.42ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 13.531}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and (`id` = 1)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.313181, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 13.531, "width_percent": 0.783}, {"sql": "select count(*) as aggregate from `organizations` where `name` = 'Tanzayb' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1)", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 875}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 652}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": **********.356044, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "UniqueManyToMany.php:55", "source": {"index": 16, "namespace": null, "name": "app/Rules/UniqueManyToMany.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Rules\\UniqueManyToMany.php", "line": 55}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FRules%2FUniqueManyToMany.php&line=55", "ajax": false, "filename": "UniqueManyToMany.php", "line": "55"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 14.314, "width_percent": 0.997}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.365561, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:230", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 230}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=230", "ajax": false, "filename": "Organization.php", "line": "230"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.311, "width_percent": 0}, {"sql": "insert into `organizations` (`shop_id`, `name`, `region`, `units`, `currency`, `updated_at`, `created_at`) values (null, 'Tanzayb', null, null, null, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [null, "<PERSON><PERSON><PERSON><PERSON>", null, null, null, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.366185, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "Organization.php:245", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=245", "ajax": false, "filename": "Organization.php", "line": "245"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.311, "width_percent": 1.005}, {"sql": "insert into `channels` (`organization_id`, `name`, `type`, `updated_at`, `created_at`) values (1, 'Tanzayb Store', 'shopify', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, "Tanzayb Store", "shopify", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.3723712, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "Organization.php:328", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 328}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=328", "ajax": false, "filename": "Organization.php", "line": "328"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.316, "width_percent": 1.525}, {"sql": "select count(*) as aggregate from `locations` where `organization_id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 32, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 33, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.4033108, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "Location.php:36", "source": {"index": 16, "namespace": null, "name": "app/Models/Location/Location.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Location\\Location.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FLocation%2FLocation.php&line=36", "ajax": false, "filename": "Location.php", "line": "36"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 17.841, "width_percent": 0.939}, {"sql": "insert into `locations` (`organization_id`, `name`, `default_location`, `updated_at`, `created_at`) values (1, 'Tanzayb Store Warehouse', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, "Tanzayb Store Warehouse", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.407368, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "Organization.php:334", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 334}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=334", "ajax": false, "filename": "Organization.php", "line": "334"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 18.78, "width_percent": 0.766}, {"sql": "insert into `channel_location` (`channel_id`, `location_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.411481, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "Organization.php:339", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 339}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=339", "ajax": false, "filename": "Organization.php", "line": "339"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 19.547, "width_percent": 2.085}, {"sql": "insert into `versions` (`name`, `organization_id`, `is_default`, `currency`, `separator`, `updated_at`, `created_at`) values ('EN-US', 1, 1, 'USD', '.', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["EN-US", 1, 1, "USD", ".", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.418818, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "Organization.php:349", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 349}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=349", "ajax": false, "filename": "Organization.php", "line": "349"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.632, "width_percent": 1.5}, {"sql": "insert into `channel_version` (`channel_id`, `version_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.4775958, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Organization.php:355", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 355}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=355", "ajax": false, "filename": "Organization.php", "line": "355"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 23.131, "width_percent": 1.656}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `updated_at`, `created_at`) values ('Tanzayb', 1, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON><PERSON>", 1, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.483785, "duration": 0.0025, "duration_str": "2.5ms", "memory": 0, "memory_str": null, "filename": "Folder.php:228", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 228}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=228", "ajax": false, "filename": "Folder.php", "line": "228"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 24.788, "width_percent": 2.06}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IkJDR1l2ZkIxZVUvaEN5dE9WWkZRYkE9PSIsInZhbHVlIjoiMWd5RkNHelVGaFRZb01hRzR3QUd4Zz09IiwibWFjIjoiN2VjZGZhZTA4NTQyYzUwNTEyOWNjMWMyMjY0MDMzYmQ1OTM3MTFjZWIyMTI2Y2E0YTQ3ZTVkMzE0ZmJkMjcwNiIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-02 07:49:23' where `id` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IkJDR1l2ZkIxZVUvaEN5dE9WWkZRYkE9PSIsInZhbHVlIjoiMWd5RkNHelVGaFRZb01hRzR3QUd4Zz09IiwibWFjIjoiN2VjZGZhZTA4NTQyYzUwNTEyOWNjMWMyMjY0MDMzYmQ1OTM3MTFjZWIyMTI2Y2E0YTQ3ZTVkMzE0ZmJkMjcwNiIsInRhZyI6IiJ9", "2025-07-02 07:49:23", 1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 230}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 359}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.49028, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.848, "width_percent": 0.758}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Instagram', 1, 1, 'social_media', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Instagram", 1, 1, "social_media", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.494357, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.606, "width_percent": 0.437}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IjFZdDJ1VVlEcUtKK2dtOFJlWTZrVlE9PSIsInZhbHVlIjoiVGZQMWpmb01HQUEyNVVpd1ZLY05oZz09IiwibWFjIjoiMjI4OTViYTFmNGE2ZjRmMmU3ZGY1MTUyYmJiNjg5ZjU2ZTk5MDllZDM2ZTMxNzk4ZWU2YmEwN2I0MTZhMDdkNSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-02 07:49:23' where `id` = 2 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IjFZdDJ1VVlEcUtKK2dtOFJlWTZrVlE9PSIsInZhbHVlIjoiVGZQMWpmb01HQUEyNVVpd1ZLY05oZz09IiwibWFjIjoiMjI4OTViYTFmNGE2ZjRmMmU3ZGY1MTUyYmJiNjg5ZjU2ZTk5MDllZDM2ZTMxNzk4ZWU2YmEwN2I0MTZhMDdkNSIsInRhZyI6IiJ9", "2025-07-02 07:49:23", 2, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.497849, "duration": 0.01616, "duration_str": "16.16ms", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.043, "width_percent": 13.317}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Youtube', 1, 1, 'social_media', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Youtube", 1, 1, "social_media", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.51685, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.36, "width_percent": 0.42}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6InkxQWdBQ29YNEVqMjlBVFBaa3BhNkE9PSIsInZhbHVlIjoicVo3K0s5YnBYbHp5Y2Z4MndyL2VNQT09IiwibWFjIjoiYzc3ZjA3ODVlODRiODRhMGY0OTY3MWY3MDRlYzEwOGE5N2I0M2IxNWI3OTJiZTYyZjk3N2RjOGU1M2U2YzlmZSIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-02 07:49:23' where `id` = 3 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6InkxQWdBQ29YNEVqMjlBVFBaa3BhNkE9PSIsInZhbHVlIjoicVo3K0s5YnBYbHp5Y2Z4MndyL2VNQT09IiwibWFjIjoiYzc3ZjA3ODVlODRiODRhMGY0OTY3MWY3MDRlYzEwOGE5N2I0M2IxNWI3OTJiZTYyZjk3N2RjOGU1M2U2YzlmZSIsInRhZyI6IiJ9", "2025-07-02 07:49:23", 3, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.520038, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 41.78, "width_percent": 0.503}, {"sql": "insert into `folders` (`name`, `organization_id`, `is_default`, `type`, `updated_at`, `created_at`) values ('Facebook', 1, 1, 'social_media', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Facebook", 1, 1, "social_media", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.524765, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Folder.php:242", "source": {"index": 15, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 242}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=242", "ajax": false, "filename": "Folder.php", "line": "242"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.283, "width_percent": 0.544}, {"sql": "update `folders` set `link` = 'guests/eyJpdiI6IktBa3hjRy9DODdmN0F1eWN0S0hxTnc9PSIsInZhbHVlIjoiNThLQWUzSTNkWklFeEsvWitzdm9odz09IiwibWFjIjoiNzI5YWM3YzNhNDkzMGVmZDE3MzI2YTQzMDI4NDgwNTI4OWFjMmRkMmNlZWIxMWJlY2I4YTMyNGVjMjAwYjJkZCIsInRhZyI6IiJ9', `folders`.`updated_at` = '2025-07-02 07:49:23' where `id` = 4 and `organization_id` = 1", "type": "query", "params": [], "bindings": ["guests/eyJpdiI6IktBa3hjRy9DODdmN0F1eWN0S0hxTnc9PSIsInZhbHVlIjoiNThLQWUzSTNkWklFeEsvWitzdm9odz09IiwibWFjIjoiNzI5YWM3YzNhNDkzMGVmZDE3MzI2YTQzMDI4NDgwNTI4OWFjMmRkMmNlZWIxMWJlY2I4YTMyNGVjMjAwYjJkZCIsInRhZyI6IiJ9", "2025-07-02 07:49:23", 4, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, {"index": 13, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 244}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 364}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}], "start": **********.528167, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "Folder.php:139", "source": {"index": 12, "namespace": null, "name": "packages/apimio/gallery/src/Models/Folder.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\packages\\apimio\\gallery\\src\\Models\\Folder.php", "line": 139}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fpackages%2Fapimio%2Fgallery%2Fsrc%2FModels%2FFolder.php&line=139", "ajax": false, "filename": "Folder.php", "line": "139"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.827, "width_percent": 0.585}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('General', 1, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["General", 1, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5326638, "duration": 0.0025299999999999997, "duration_str": "2.53ms", "memory": 0, "memory_str": null, "filename": "Organization.php:371", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 371}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=371", "ajax": false, "filename": "Organization.php", "line": "371"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.412, "width_percent": 2.085}, {"sql": "insert into `settings` (`key`, `organization_id`, `value`) values ('sku', 1, '1'), ('name', 1, '1'), ('barcode', 1, '0'), ('weight', 1, '1'), ('price', 1, '1'), ('compare_at_price', 1, '0'), ('cost_price', 1, '0'), ('brand', 1, '1'), ('vendor', 1, '1'), ('category', 1, '1')", "type": "query", "params": [], "bindings": ["sku", 1, "1", "name", 1, "1", "barcode", 1, "0", "weight", 1, "1", "price", 1, "1", "compare_at_price", 1, "0", "cost_price", 1, "0", "brand", 1, "1", "vendor", 1, "1", "category", 1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, {"index": 11, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.538112, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "Organization.php:373", "source": {"index": 10, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 373}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=373", "ajax": false, "filename": "Organization.php", "line": "373"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 45.496, "width_percent": 1.656}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Product Name', 'product_name', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Product Name", "product_name", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5458272, "duration": 0.00698, "duration_str": "6.98ms", "memory": 0, "memory_str": null, "filename": "Organization.php:434", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 434}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=434", "ajax": false, "filename": "Organization.php", "line": "434"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.153, "width_percent": 5.752}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.556487, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "Organization.php:439", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 439}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=439", "ajax": false, "filename": "Organization.php", "line": "439"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 52.905, "width_percent": 1.755}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Title', 'title', 13, 1, null, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Title", "title", 13, 1, null, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.5614629, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "Organization.php:449", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=449", "ajax": false, "filename": "Organization.php", "line": "449"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 54.66, "width_percent": 0.453}, {"sql": "insert into `attribute_options` (`name`, `attribute_id`, `updated_at`, `created_at`) values ('Default Title', 2, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Default Title", 2, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.565499, "duration": 0.00664, "duration_str": "6.64ms", "memory": 0, "memory_str": null, "filename": "Organization.php:454", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 454}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=454", "ajax": false, "filename": "Organization.php", "line": "454"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.113, "width_percent": 5.472}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Description', 'description', 3, 1, '{\\\"required\\\":1,\\\"max\\\":63000}', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Description", "description", 3, 1, "{\"required\":1,\"max\":63000}", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.576129, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Organization.php:463", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 463}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=463", "ajax": false, "filename": "Organization.php", "line": "463"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.585, "width_percent": 0.486}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (1, 3, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, 3, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.579395, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Organization.php:468", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 468}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=468", "ajax": false, "filename": "Organization.php", "line": "468"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.071, "width_percent": 0.47}, {"sql": "insert into `families` (`name`, `is_default`, `organization_id`, `updated_at`, `created_at`) values ('SEO', 1, 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["SEO", 1, 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.583344, "duration": 0.00691, "duration_str": "6.91ms", "memory": 0, "memory_str": null, "filename": "Organization.php:476", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 476}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=476", "ajax": false, "filename": "Organization.php", "line": "476"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 61.541, "width_percent": 5.694}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('URL Slug', 'seo_url', 11, 1, '{\\\"required\\\":1,\\\"0\\\":\\\"slug\\\",\\\"1\\\":\\\"url\\\",\\\"max\\\":255}', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["URL Slug", "seo_url", 11, 1, "{\"required\":1,\"0\":\"slug\",\"1\":\"url\",\"max\":255}", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.593967, "duration": 0.00523, "duration_str": "5.23ms", "memory": 0, "memory_str": null, "filename": "Organization.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=486", "ajax": false, "filename": "Organization.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 67.235, "width_percent": 4.31}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 4, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [2, 4, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.6019409, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:491", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 491}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=491", "ajax": false, "filename": "Organization.php", "line": "491"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.545, "width_percent": 0.461}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Title', 'seo_title', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["SEO Title", "seo_title", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.605875, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "Organization.php:501", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 501}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=501", "ajax": false, "filename": "Organization.php", "line": "501"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.007, "width_percent": 0.577}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 5, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [2, 5, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.609997, "duration": 0.00519, "duration_str": "5.19ms", "memory": 0, "memory_str": null, "filename": "Organization.php:506", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 506}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=506", "ajax": false, "filename": "Organization.php", "line": "506"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.583, "width_percent": 4.277}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('SEO Description', 'seo_description', 3, 1, '{\\\"required\\\":1,\\\"max\\\":160}', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["SEO Description", "seo_description", 3, 1, "{\"required\":1,\"max\":160}", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.618406, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "Organization.php:516", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 516}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=516", "ajax": false, "filename": "Organization.php", "line": "516"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 76.86, "width_percent": 0.47}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 6, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [2, 6, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.62165, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:521", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 521}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=521", "ajax": false, "filename": "Organization.php", "line": "521"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.33, "width_percent": 0.412}, {"sql": "insert into `attributes` (`name`, `handle`, `attribute_type_id`, `organization_id`, `rules`, `is_default`, `updated_at`, `created_at`) values ('Tags', 'seo_keyword', 1, 1, '{\\\"required\\\":1,\\\"max\\\":255}', 1, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": ["Tags", "seo_keyword", 1, 1, "{\"required\":1,\"max\":255}", 1, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.62626, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Organization.php:531", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 531}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=531", "ajax": false, "filename": "Organization.php", "line": "531"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.742, "width_percent": 0.527}, {"sql": "insert into `attribute_family` (`family_id`, `attribute_id`, `updated_at`, `created_at`) values (2, 7, '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [2, 7, "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}], "start": **********.62955, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Organization.php:536", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 536}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=536", "ajax": false, "filename": "Organization.php", "line": "536"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.269, "width_percent": 0.396}, {"sql": "select `id` from `families` where `is_default` = 1 and `organization_id` = 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 23, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 24, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.633135, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Organization.php:554", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 554}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=554", "ajax": false, "filename": "Organization.php", "line": "554"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.665, "width_percent": 0.569}, {"sql": "select * from `attribute_family` where `family_id` in (1, 2)", "type": "query", "params": [], "bindings": [1, 2], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.636595, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.234, "width_percent": 0.461}, {"sql": "select * from `attributes` where `attributes`.`id` in (1, 3, 4, 5, 6, 7) and `organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 22, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 29, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 30, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.7288852, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "Organization.php:555", "source": {"index": 20, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 555}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=555", "ajax": false, "filename": "Organization.php", "line": "555"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 79.695, "width_percent": 0.544}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (1, 1, 'title', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [1, 1, "title", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.732997, "duration": 0.00264, "duration_str": "2.64ms", "memory": 0, "memory_str": null, "filename": "Organization.php:563", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 563}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=563", "ajax": false, "filename": "Organization.php", "line": "563"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.239, "width_percent": 2.176}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (2, 1, 'body_html', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [2, 1, "body_html", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.7385828, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Organization.php:566", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 566}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=566", "ajax": false, "filename": "Organization.php", "line": "566"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.415, "width_percent": 0.503}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (4, 1, 'metafields_global_title_tag', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [4, 1, "metafields_global_title_tag", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.742821, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "Organization.php:581", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 581}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=581", "ajax": false, "filename": "Organization.php", "line": "581"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 82.917, "width_percent": 0.412}, {"sql": "insert into `product_shopify_mappings` (`attribute_family_id`, `organization_id`, `shopify_fields`, `updated_at`, `created_at`) values (5, 1, 'metafields_global_description_tag', '2025-07-02 07:49:23', '2025-07-02 07:49:23')", "type": "query", "params": [], "bindings": [5, 1, "metafields_global_description_tag", "2025-07-02 07:49:23", "2025-07-02 07:49:23"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 537}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 24, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.746089, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:584", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 584}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=584", "ajax": false, "filename": "Organization.php", "line": "584"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.329, "width_percent": 0.461}, {"sql": "select count(*) as aggregate from `invites` where `email` = '<EMAIL>' and `is_accepted` = '0' and `is_declined` = '0'", "type": "query", "params": [], "bindings": ["<EMAIL>", "0", "0"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 544}, {"index": 18, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 109}, {"index": 25, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 245}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}], "start": **********.753633, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "Invite.php:124", "source": {"index": 16, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 124}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=124", "ajax": false, "filename": "Invite.php", "line": "124"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.791, "width_percent": 1.343}, {"sql": "select * from `invites` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 250}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.7590258, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "Invite.php:486", "source": {"index": 15, "namespace": null, "name": "app/Models/Invite/Invite.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Invite\\Invite.php", "line": 486}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FInvite%2FInvite.php&line=486", "ajax": false, "filename": "Invite.php", "line": "486"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.134, "width_percent": 0.643}, {"sql": "update `organizations` set `stripe_id` = 'cus_SbYXEpMdgfM78G', `organizations`.`updated_at` = '2025-07-02 07:49:31' where `id` = 1", "type": "query", "params": [], "bindings": ["cus_SbYXEpMdgfM78G", "2025-07-02 07:49:31", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 252}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9452958, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ManagesCustomer.php:97", "source": {"index": 14, "namespace": null, "name": "vendor/laravel/cashier/src/Concerns/ManagesCustomer.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\cashier\\src\\Concerns\\ManagesCustomer.php", "line": 97}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fcashier%2Fsrc%2FConcerns%2FManagesCustomer.php&line=97", "ajax": false, "filename": "ManagesCustomer.php", "line": "97"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 85.777, "width_percent": 0.626}, {"sql": "update `organizations` set `trial_ends_at` = '2025-07-16 07:49:31', `organizations`.`updated_at` = '2025-07-02 07:49:31' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-16 07:49:31", "2025-07-02 07:49:31", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.949883, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "Organization.php:254", "source": {"index": 14, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 254}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=254", "ajax": false, "filename": "Organization.php", "line": "254"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.403, "width_percent": 0.593}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.954102, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "Organization.php:263", "source": {"index": 21, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 263}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=263", "ajax": false, "filename": "Organization.php", "line": "263"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.996, "width_percent": 0.882}, {"sql": "select * from `organization_user` where `organization_user`.`organization_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.958147, "duration": 0.00134, "duration_str": "1.34ms", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.878, "width_percent": 1.104}, {"sql": "insert into `organization_user` (`organization_id`, `user_id`) values (1, 1)", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, {"index": 14, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.962581, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "Organization.php:277", "source": {"index": 13, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 277}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=277", "ajax": false, "filename": "Organization.php", "line": "277"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 88.982, "width_percent": 0.89}, {"sql": "select `id` from `organization_user` where `user_id` = 1 and `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.967286, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "Organization.php:280", "source": {"index": 17, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 280}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=280", "ajax": false, "filename": "Organization.php", "line": "280"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.872, "width_percent": 0.569}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9724412, "duration": 0.0020299999999999997, "duration_str": "2.03ms", "memory": 0, "memory_str": null, "filename": "Organization.php:283", "source": {"index": 16, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 283}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=283", "ajax": false, "filename": "Organization.php", "line": "283"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 90.441, "width_percent": 1.673}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 1, '2025-07-02 07:49:31', '2025-07-02 07:49:31')", "type": "query", "params": [], "bindings": [1, 1, "2025-07-02 07:49:31", "2025-07-02 07:49:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.978381, "duration": 0.00319, "duration_str": "3.19ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.114, "width_percent": 2.629}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 2, '2025-07-02 07:49:31', '2025-07-02 07:49:31')", "type": "query", "params": [], "bindings": [1, 2, "2025-07-02 07:49:31", "2025-07-02 07:49:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.98503, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.742, "width_percent": 0.437}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 3, '2025-07-02 07:49:31', '2025-07-02 07:49:31')", "type": "query", "params": [], "bindings": [1, 3, "2025-07-02 07:49:31", "2025-07-02 07:49:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.989945, "duration": 0.00477, "duration_str": "4.77ms", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.179, "width_percent": 3.931}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 4, '2025-07-02 07:49:31', '2025-07-02 07:49:31')", "type": "query", "params": [], "bindings": [1, 4, "2025-07-02 07:49:31", "2025-07-02 07:49:31"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.9979699, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.11, "width_percent": 0.461}, {"sql": "insert into `organization_user_permissions` (`organization_user_id`, `permission_id`, `updated_at`, `created_at`) values (1, 5, '2025-07-02 07:49:32', '2025-07-02 07:49:32')", "type": "query", "params": [], "bindings": [1, 5, "2025-07-02 07:49:32", "2025-07-02 07:49:32"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.001831, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Organization.php:290", "source": {"index": 15, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 290}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=290", "ajax": false, "filename": "Organization.php", "line": "290"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 99.571, "width_percent": 0.429}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, {"index": 10, "namespace": null, "name": "app/Http/Controllers/Api/OrganizationController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\OrganizationController.php", "line": 90}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.015287, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "Organization.php:298", "source": {"index": 9, "namespace": null, "name": "app/Models/Organization/Organization.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Organization\\Organization.php", "line": 298}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=298", "ajax": false, "filename": "Organization.php", "line": "298"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\Product\\AttributeFamily": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttributeFamily.php&line=1", "ajax": false, "filename": "AttributeFamily.php", "line": "?"}}, "App\\Models\\Product\\Attribute": {"value": 6, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FAttribute.php&line=1", "ajax": false, "filename": "Attribute.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\User": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Product\\Family": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FFamily.php&line=1", "ajax": false, "filename": "Family.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}}, "count": 22, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/onboarding\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "password_hash_web": "null", "url": "array:1 [\n  \"intended\" => \"http://localhost:8000/dashboard\"\n]", "organization_id": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.store", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@store", "uri": "POST api/2024-12/organization", "controller": "App\\Http\\Controllers\\Api\\OrganizationController@store<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FOrganizationController.php&line=67\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/OrganizationController.php:67-111</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4af33c-3b20-4cdb-978e-97bb8c71674f\" target=\"_blank\">View in Telescope</a>", "duration": "9.5s", "peak_memory": "40MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1904759337 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1904759337\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-753732140 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Tanzayb</span>\"\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n  \"<span class=sf-dump-key>currency</span>\" => \"<span class=sf-dump-str title=\"3 characters\">USD</span>\"\n  \"<span class=sf-dump-key>separator</span>\" => \"<span class=sf-dump-str>.</span>\"\n  \"<span class=sf-dump-key>weightUnit</span>\" => \"<span class=sf-dump-str title=\"2 characters\">oz</span>\"\n  \"<span class=sf-dump-key>trial_ends_at</span>\" => \"<span class=sf-dump-str title=\"10 characters\">2025-07-16</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-753732140\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-481790557 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">88</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjgvSGd6UkNJb1ZrMEhxbkJHcis1N3c9PSIsInZhbHVlIjoiSXhpUmc1U2JoeXIrNGc5aVVWNzBKdTNrVzh4YkhkZnA3WUpPTUUxN2hGYXpyMHFGN0F0czdGRWpXZitGWEJUaTBjNWg0a1RLdDlIZ0tsRUJSdmFCQytkMTREZmI2ZXlScHEzbVlxR29kcnN5TFJPbERtb0RwRnR4dHZlaUg1bGgiLCJtYWMiOiI2N2UxY2RmNGZjMWE0YTA4NTA2ZGIyOWJmMTFjNGQ3NTM1YTVhNmU4NGYyNWU2YmJjMzljNTU4MGEyNWYzOTRmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjgvSGd6UkNJb1ZrMEhxbkJHcis1N3c9PSIsInZhbHVlIjoiSXhpUmc1U2JoeXIrNGc5aVVWNzBKdTNrVzh4YkhkZnA3WUpPTUUxN2hGYXpyMHFGN0F0czdGRWpXZitGWEJUaTBjNWg0a1RLdDlIZ0tsRUJSdmFCQytkMTREZmI2ZXlScHEzbVlxR29kcnN5TFJPbERtb0RwRnR4dHZlaUg1bGgiLCJtYWMiOiI2N2UxY2RmNGZjMWE0YTA4NTA2ZGIyOWJmMTFjNGQ3NTM1YTVhNmU4NGYyNWU2YmJjMzljNTU4MGEyNWYzOTRmIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6ImpnbHJTekVaOUxpc0NVK3ROQkJHRmc9PSIsInZhbHVlIjoiQU5MNnBDTVk1dHhYWUh5azJlL2VQUkY4OWJVVXNzSlRoWmh2cy9xQTBYNlNReXVEYXExdnliQnJOciswRkpCS214RUR1K2pxNkZSS0Rsem8yNkdPSkIvSDh1bnUza3MvdTJYUXo5ZXVxUXBNd3RYVTVmdTZwYlNkY1VTQ0tiSWsiLCJtYWMiOiI0NGU5NGFhMDcyNzc1OWExM2RkM2ZkYmYwYzcxNWIxYjM1YWIwNGNmMzdlODdhNWEwNTYxZmMwMjU2YmEwNTM2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481790557\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1594892219 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zvOZSvHzZnsYe7BByH4nzLoQwwGRhsRK0dRNgSNd</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594892219\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1540827902 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:49:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlQ0Mk1jV05HMVkxSFVINjdRZ25YYWc9PSIsInZhbHVlIjoiSFdPZ2dlQkJZckJkTnZCeVhYaFlNU1B0VW9NSDFnSjdzWGdEdXBZckdVcHZ2dnhRMnhHc0c0dy9FYmYreUdKTjd3SldNWUE4ZmplcU01dGF6U1RmWDByMm9hU3dRYzd3aHZSWDRYTVZhVUVZdm1sTmI5VDVEVEJKQi9ZU1VHNFoiLCJtYWMiOiIxYWI0MTk0NjAxNWJlNzljMTkwNGEzODdiZTQyZTA0MThjMzQ1MTQ4YTdmNjkyYmZiMGY2YmZmOWI5ZWIxYzVmIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:49:32 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6ImN1S2lvWUh4cmFwL2N1emlXQW5QZXc9PSIsInZhbHVlIjoiZzR4YS9MOG96cUMxeElWL2YyWFdwYVphTHNyc05wQzB4aG4wZHcxUlA0bGozWlVNV3FOcGNmNjVxSVpWbysvTHBTc3pPVWlTeFJ5YlpiU3YybXRxMkc1RHJ6TXo1Mlp4OUtqRDl0WThYb2lLMjNHZEtWV0R6WUhadjZYcXpmNlUiLCJtYWMiOiJlOTU4Mzc0MjQwZjdjY2Q3NjFlMjQ2ZGU1MjcxOWY4ZWI0YTgwZWUwMDFjMzA0ODQyMGRlNWU3MTQyOGJkYTgwIiwidGFnIjoiIn0%3D; expires=Wed, 02 Jul 2025 09:49:32 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlQ0Mk1jV05HMVkxSFVINjdRZ25YYWc9PSIsInZhbHVlIjoiSFdPZ2dlQkJZckJkTnZCeVhYaFlNU1B0VW9NSDFnSjdzWGdEdXBZckdVcHZ2dnhRMnhHc0c0dy9FYmYreUdKTjd3SldNWUE4ZmplcU01dGF6U1RmWDByMm9hU3dRYzd3aHZSWDRYTVZhVUVZdm1sTmI5VDVEVEJKQi9ZU1VHNFoiLCJtYWMiOiIxYWI0MTk0NjAxNWJlNzljMTkwNGEzODdiZTQyZTA0MThjMzQ1MTQ4YTdmNjkyYmZiMGY2YmZmOWI5ZWIxYzVmIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:49:32 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6ImN1S2lvWUh4cmFwL2N1emlXQW5QZXc9PSIsInZhbHVlIjoiZzR4YS9MOG96cUMxeElWL2YyWFdwYVphTHNyc05wQzB4aG4wZHcxUlA0bGozWlVNV3FOcGNmNjVxSVpWbysvTHBTc3pPVWlTeFJ5YlpiU3YybXRxMkc1RHJ6TXo1Mlp4OUtqRDl0WThYb2lLMjNHZEtWV0R6WUhadjZYcXpmNlUiLCJtYWMiOiJlOTU4Mzc0MjQwZjdjY2Q3NjFlMjQ2ZGU1MjcxOWY4ZWI0YTgwZWUwMDFjMzA0ODQyMGRlNWU3MTQyOGJkYTgwIiwidGFnIjoiIn0%3D; expires=Wed, 02-Jul-2025 09:49:32 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1540827902\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1027258196 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">http://localhost:8000/onboarding</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>organization_id</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1027258196\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/organization", "action_name": "organization.store", "controller_action": "App\\Http\\Controllers\\Api\\OrganizationController@store"}, "badge": null}}