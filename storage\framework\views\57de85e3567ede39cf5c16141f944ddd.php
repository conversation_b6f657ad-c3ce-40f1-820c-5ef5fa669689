<?php ?>


<?php if(Request::segment(4) == 'edit'): ?>
    <?php $__env->startSection('titles','Edit Vendor'); ?>
<?php else: ?>
    <?php $__env->startSection('titles','Add Vendors'); ?>
<?php endif; ?>
<?php $__env->startSection('content'); ?>

    <div>
        <?php if (isset($component)) { $__componentOriginal7a044565d18b640bc08243f7f8ff3e54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7a044565d18b640bc08243f7f8ff3e54 = $attributes; } ?>
<?php $component = App\View\Components\Products\AddPageTitle::resolve(['name' => ''.e(trans('products_vendors.page_title')).'','routes' => route('vendors.index')] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.add-page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\AddPageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes(['type' => 'false']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7a044565d18b640bc08243f7f8ff3e54)): ?>
<?php $attributes = $__attributesOriginal7a044565d18b640bc08243f7f8ff3e54; ?>
<?php unset($__attributesOriginal7a044565d18b640bc08243f7f8ff3e54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7a044565d18b640bc08243f7f8ff3e54)): ?>
<?php $component = $__componentOriginal7a044565d18b640bc08243f7f8ff3e54; ?>
<?php unset($__componentOriginal7a044565d18b640bc08243f7f8ff3e54); ?>
<?php endif; ?>


       
        <div class="row">
            <div class="col-12 col-md-9 col-lg-9 col-xl-6">
                <form id="pro_ven_create_form" class="formStyle" action="<?php echo e(isset($vendor) ? route('vendors.update',$vendor->id) : route('vendors.store')); ?>"

                      method="POST">
                    <?php echo csrf_field(); ?>
                    <?php if(isset($vendor)): ?>
                        <?php echo method_field('PUT'); ?>
                        <input type="hidden" name="id" value="<?php echo e($vendor->id); ?>">
                    <?php endif; ?>
                    <div class="form-group mt-4">
                        <label for="fname"><?php echo e(trans('products_vendors_create.vendor_name')); ?>&nbsp;<span class="text-danger-light">*</span></label>
                        <input type="text" class="form-control  <?php $__errorArgs = ['fname'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="fname"
                               name="fname" value="<?php echo e(isset($vendor) ? $vendor->fname : old('fname')); ?>" autofocus>
                        <?php $__errorArgs = ['fname'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <span class="text-danger">
                        <small><?php echo e($message); ?></small>
                        </span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="form-group mb-4 mt-40">

                        <div class="d-flex justify-content-end">
                                <a href="<?php echo e(route('vendors.index')); ?>" class="btn btn-outline-danger">
                                    <?php echo e(trans('products_vendors_create.cancel_btn')); ?>

                                </a>

                                <button type="submit" id="pro_ven_create_btn"
                                        class=" btn btn-primary ms-2">
                                    <?php echo e(trans('products_vendors_create.save_btn')); ?>

                                </button>
                               
                            
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/vendors/add.blade.php ENDPATH**/ ?>