<?php ?>

<?php $__env->startSection('titles','Brands'); ?>
<?php $__env->startSection('content'); ?>
    <div>
        <?php if (isset($component)) { $__componentOriginal262b0e1ee858dd683d724746646aea00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal262b0e1ee858dd683d724746646aea00 = $attributes; } ?>
<?php $component = App\View\Components\Products\PageTitle::resolve(['name' => ''.e(trans('products_brands.page_title')).'','description' => ''.e(trans('products_brands.page_description')).'','links' => 'true','button' => 'true'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\PageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('addbutton', null, []); ?> 
                <a href="<?php echo e(route('brands.create')); ?>"
                   id="add-brands"
                   class="btn btn-primary float-lg-right float-md-right only-disabled">
                <?php echo e(trans('products_brands.add_vendor_btn')); ?>

                </a>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $attributes = $__attributesOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__attributesOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $component = $__componentOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__componentOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
        <div class="row">
            <div class="col-12 col-md-6 col-xl-3">
                <?php if(count($data["brands"]) > 0): ?>
                    <?php if (isset($component)) { $__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142 = $attributes; } ?>
<?php $component = App\View\Components\General\SearchBar::resolve(['placeholder' => ''.e(trans('products_brands.search_placeholder')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.search-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\SearchBar::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142)): ?>
<?php $attributes = $__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142; ?>
<?php unset($__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142)): ?>
<?php $component = $__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142; ?>
<?php unset($__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
                <div class="row mt-2">
                    <div class="col-12">
                        <?php if(count($data['brands']) > 0): ?>
                                <table class="table">
                                    <caption style="visibility: hidden"></caption>
                                    <thead class="thead">
                                    <tr>
                                        <th scope="col"><?php echo e(__('Name')); ?></th>
                                        <th class="text-end"><?php echo e(__('Actions')); ?></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $data['brands']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e(substr($brand->name,0,50)); ?></td>
                                            <td class="text-end">
                                                <a href="<?php echo e(route('brands.edit', $brand->id)); ?>"
                                                   class="pro_brand_edit mr-3 edit-btn text-decoration-none">
                                                    <i class="fa-regular fa-pen-to-square fs-20"></i>
                                                </a>
                                                <a href="#" data-id="<?php echo e($brand->id); ?>" data-retailer-name=""
                                                   data-bs-toggle="modal" data-bs-target="#delete-modal-<?php echo e($brand->id); ?>" class="btn-delete text-decoration-none">
                                                    <i class="fa-regular fa-trash-can fs-20 text-danger"></i>
                                                </a>
                                            </td>
                                            <?php if (isset($component)) { $__componentOriginal263b257ff8b885fac63383b74921da78 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal263b257ff8b885fac63383b74921da78 = $attributes; } ?>
<?php $component = App\View\Components\Assets\DeleteModal::resolve(['id' => ''.e($brand->id).'','text' => 'Are you sure you want to delete this brand?','button' => 'Delete Brand','title' => 'Delete Brand','url' => ''.e(route('brands.destroy',$brand->id)).'','type' => 'brand'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('assets.delete-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Assets\DeleteModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal263b257ff8b885fac63383b74921da78)): ?>
<?php $attributes = $__attributesOriginal263b257ff8b885fac63383b74921da78; ?>
<?php unset($__attributesOriginal263b257ff8b885fac63383b74921da78); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal263b257ff8b885fac63383b74921da78)): ?>
<?php $component = $__componentOriginal263b257ff8b885fac63383b74921da78; ?>
<?php unset($__componentOriginal263b257ff8b885fac63383b74921da78); ?>
<?php endif; ?>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                                <?php echo $data['brands']->appends($request->all())->links(); ?>

                        <?php else: ?>
                            <?php if (isset($component)) { $__componentOriginal9c590450895f60ce3af8182e3a1a843e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c590450895f60ce3af8182e3a1a843e = $attributes; } ?>
<?php $component = App\View\Components\General\EmptyPage::resolve(['description' => ''.e(trans('products_brands.page_empty')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.empty-page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\EmptyPage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $attributes = $__attributesOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $component = $__componentOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__componentOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </div>

        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer_scripts'); ?>
    <script type="text/javascript">
        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        $("#add-modal").modal("show");
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/brands/view.blade.php ENDPATH**/ ?>