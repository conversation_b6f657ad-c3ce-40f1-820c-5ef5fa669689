{"__meta": {"id": "01JZ7EV24Y12JV8VVJS23WZ6EK", "datetime": "2025-07-03 06:16:02", "utime": **********.976307, "method": "GET", "uri": "/api/2024-12/shopify-sync-status", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[06:16:02] LOG.warning: Optional parameter $id declared before required parameter $version_id is implicitly treated as a required parameter in C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Models\\Channel\\ShopifyChannel.php on line 716", "message_html": null, "is_string": false, "label": "warning", "time": **********.955177, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.165117, "end": **********.976403, "duration": 0.8112859725952148, "duration_str": "811ms", "measures": [{"label": "Booting", "start": **********.165117, "relative_start": 0, "end": **********.862782, "relative_end": **********.862782, "duration": 0.****************, "duration_str": "698ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.862795, "relative_start": 0.****************, "end": **********.976411, "relative_end": 8.106231689453125e-06, "duration": 0.****************, "duration_str": "114ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.87928, "relative_start": 0.***************, "end": **********.883371, "relative_end": **********.883371, "duration": 0.004091024398803711, "duration_str": "4.09ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.9683, "relative_start": 0.****************, "end": **********.96886, "relative_end": **********.96886, "duration": 0.0005598068237304688, "duration_str": "560μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.971, "relative_start": 0.****************, "end": **********.971159, "relative_end": **********.971159, "duration": 0.0001590251922607422, "duration_str": "159μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET api/2024-12/shopify-sync-status", "middleware": "api, auth:sanctum", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:185-222</a>"}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02388, "accumulated_duration_str": "23.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 57}, {"index": 23, "namespace": null, "name": "vendor/laravel/sanctum/src/Http/Middleware/AuthenticateSession.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\AuthenticateSession.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.911582, "duration": 0.022969999999999997, "duration_str": "22.97ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 96.189}, {"sql": "select * from `channels` where `organization_id` = '1' and exists (select * from `shopify_channels` where `channels`.`id` = `shopify_channels`.`channel_id`)", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 189}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.95972, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "DashboardController.php:189", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Api/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Api\\DashboardController.php", "line": 189}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=189", "ajax": false, "filename": "DashboardController.php", "line": "189"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 96.189, "width_percent": 3.811}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/shopify-sync-status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "organization_id": "1"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "action_name": null, "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus", "uri": "GET api/2024-12/shopify-sync-status", "controller": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "prefix": "api/2024-12", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FApi%2FDashboardController.php&line=185\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Api/DashboardController.php:185-222</a>", "middleware": "api", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4cd4c9-684c-41d1-98e4-e4a4bdf8f806\" target=\"_blank\">View in Telescope</a>", "duration": "815ms", "peak_memory": "32MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1113107461 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1113107461\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1742279219 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1742279219\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2132352449 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Bearer null******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImxkcERzMElkZE4xdEF6K1dvbTFXL3c9PSIsInZhbHVlIjoicEdZM0ZHUlZ6RjhabjFieVpXUWQzMFYrNFV5MWkwRlREbGFBbWErcW5CVGx2NmNPNG9tQXVJdy90U2tJQkw1Y0g5eWhoN1Zjc2hIQm9USnFlOE1PSTFMWGlDcjhlTTlUdjBkQzRHSldoYVBDaHFWaldkTVBvZlRuZ0c5OHZUT2YiLCJtYWMiOiIwMDlkMWJjNDk2ODY0YTliOWRkZjA2MDY1M2UwMjlhNzU4NjZjNWU5NTE5ODI5YTQ1ZTA4YTBkNzhlYjliN2RmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">application/json, text/plain, */*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6ImxkcERzMElkZE4xdEF6K1dvbTFXL3c9PSIsInZhbHVlIjoicEdZM0ZHUlZ6RjhabjFieVpXUWQzMFYrNFV5MWkwRlREbGFBbWErcW5CVGx2NmNPNG9tQXVJdy90U2tJQkw1Y0g5eWhoN1Zjc2hIQm9USnFlOE1PSTFMWGlDcjhlTTlUdjBkQzRHSldoYVBDaHFWaldkTVBvZlRuZ0c5OHZUT2YiLCJtYWMiOiIwMDlkMWJjNDk2ODY0YTliOWRkZjA2MDY1M2UwMjlhNzU4NjZjNWU5NTE5ODI5YTQ1ZTA4YTBkNzhlYjliN2RmIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6IldlUHZjT0JXV3UrdGt2WFhLeVArWFE9PSIsInZhbHVlIjoiWkFSOStKWVpJWFJodUdXZXVyOEJwRlFSWXd1M01OYXhIaWxOeWpZcnk3NTk4SlZMaXYwa3kvSSt3UlpjZVc3Nzc3aVJBOFcvWi9IZlFZRzQ3WFQ1RUc4YUY0MkUyOWtpdlJyelRxd2NQekRMd1FnWDBTbkpPRk4xdEF0NFB5N1UiLCJtYWMiOiIxNDlhZmY0YTAzZWNhZTI1M2ZiMzRlMzZkYWIyOGY5ZjRjYjBlN2FkMjQ3ZGE2MjJhYWE2YWNkNjFmNDVhNmJkIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2132352449\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1508386738 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hpj02iYobWSFRbp0Zjz10UyQzMp42LbVgb86a16Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508386738\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-35856311 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:16:02 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-limit</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">60</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-ratelimit-remaining</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">51</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Ildkb3BLSkFsbnlGVlNNOTBxMXFzbWc9PSIsInZhbHVlIjoiRS96bE9sRFRwRVF3cDNYbFE1UVVpcVdkb0JiT0g4endwcnJzdDM1aUNuUTZiZWtMbFBJbXJhbnV5OXBWc2ptbmwxOHBzUkpuL053Z29qQ1VpSUdRQ1ZLTjZlOTN3SVRjWXpDb2Q0K3YvdmtLSEV4R0V4YlYxSWl4YzgrbkFhNTciLCJtYWMiOiI1YzEyMzJjMjMzMzkzOTYzYmI4ZWEyNmY2MjQyOWMwYzJlMmU5NWY2OTRiOTYzOWQ1MjgyYWU0NTU5NmQ1ZGE1IiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 08:16:02 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"448 characters\">apimio_local_session=eyJpdiI6ImlvU0xuSEZFdDVjWFVpQU04WEFUa3c9PSIsInZhbHVlIjoiRTZybFZKWXBnTm1QOGRzZlVFTTdKMjM5Y2d1QzUrZFNSZWlwM0FWOWhHV0h0RHlwZWdZdzZJKzNod3BRWmt3YXd0akNYdTNLd3hZSFFsbjMyNFBKazhXdHRkeGpTaVhlQ1RMSTl3YzJubXNBc1ZnVkk4RHRoMy9hMHBlazhrRFciLCJtYWMiOiIwMTU2ODFmMzA0OTBkYjZhNmM5ODhmZmRkMTAyNWJmMTFlNDlmZmM3ZDg2YmU5ZjE3MTVlYzU3ZWE4ZWU4OTkzIiwidGFnIjoiIn0%3D; expires=Thu, 03 Jul 2025 08:16:02 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Ildkb3BLSkFsbnlGVlNNOTBxMXFzbWc9PSIsInZhbHVlIjoiRS96bE9sRFRwRVF3cDNYbFE1UVVpcVdkb0JiT0g4endwcnJzdDM1aUNuUTZiZWtMbFBJbXJhbnV5OXBWc2ptbmwxOHBzUkpuL053Z29qQ1VpSUdRQ1ZLTjZlOTN3SVRjWXpDb2Q0K3YvdmtLSEV4R0V4YlYxSWl4YzgrbkFhNTciLCJtYWMiOiI1YzEyMzJjMjMzMzkzOTYzYmI4ZWEyNmY2MjQyOWMwYzJlMmU5NWY2OTRiOTYzOWQ1MjgyYWU0NTU5NmQ1ZGE1IiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 08:16:02 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"420 characters\">apimio_local_session=eyJpdiI6ImlvU0xuSEZFdDVjWFVpQU04WEFUa3c9PSIsInZhbHVlIjoiRTZybFZKWXBnTm1QOGRzZlVFTTdKMjM5Y2d1QzUrZFNSZWlwM0FWOWhHV0h0RHlwZWdZdzZJKzNod3BRWmt3YXd0akNYdTNLd3hZSFFsbjMyNFBKazhXdHRkeGpTaVhlQ1RMSTl3YzJubXNBc1ZnVkk4RHRoMy9hMHBlazhrRFciLCJtYWMiOiIwMTU2ODFmMzA0OTBkYjZhNmM5ODhmZmRkMTAyNWJmMTFlNDlmZmM3ZDg2YmU5ZjE3MTVlYzU3ZWE4ZWU4OTkzIiwidGFnIjoiIn0%3D; expires=Thu, 03-Jul-2025 08:16:02 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-35856311\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1274950543 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"53 characters\">http://localhost:8000/api/2024-12/shopify-sync-status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274950543\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/2024-12/shopify-sync-status", "controller_action": "App\\Http\\Controllers\\Api\\DashboardController@shopifySyncStatus"}, "badge": null}}