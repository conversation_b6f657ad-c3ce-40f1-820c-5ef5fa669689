<div class="row">
    <div class="col-12 d-flex align-items-center justify-content-between">
        <div class="col-6>
        <h2 class="clr-grey mb-0">
            <a href="<?php echo e($routes ?? ''); ?>" class="clr-grey text-decoration-none">
                <?php echo e($name ?? ''); ?>

                
            </a>
            <?php echo e(__(' /')); ?>

            <sapn class="text-dark"> Create a New <?php if(isset($name)): ?>
                <?php switch($name):
                    case ('Manage Categories'): ?>
                        Category
                        <?php break; ?>
                    <?php case ('Manage Attribute Sets'): ?>
                        Attribute Set
                        <?php break; ?>
                    <?php case ('Manage Attributes'): ?>
                        Attribute
                        <?php break; ?>
                    <?php case ('Manage Your Brands'): ?>
                        Brand
                        <?php break; ?>
                    <?php case ('Manage Languages'): ?>
                        Language
                        <?php break; ?>
                    <?php case ('Manage Locations'): ?>
                        Location
                        <?php break; ?>
                    <?php case ('Manage Stores'): ?>
                        Store
                        <?php break; ?>
                    <?php case ('Manage Vendors'): ?>
                        Vendor
                        <?php break; ?>
                    <?php default: ?>
                        <?php echo e($name); ?>

                <?php endswitch; ?>
            <?php endif; ?>
        </sapn>
            </h2>
            <?php if($name == 'Manage Categories'): ?>
                <p class="mb-0"> Define a new category to better organize your product offerings. </p>
            <?php elseif($name == 'Manage Attribute Sets'): ?>
                <p class="mb-0"> Define a new set of attributes to streamline product management. </p>
            <?php elseif($name == 'Manage Attributes'): ?>
                <p class="mb-0"> Choose the attribute type and enter the details to add a new product attribute. </p>
            <?php elseif($name == 'Manage Your Brands'): ?>
                <p class="mb-0"> Fill in the details below to add a new brand to your product catalog. </p>
            <?php elseif($name == 'Manage Languages'): ?>
                <p class="mb-0"> Enter the details to add a new language to your platform. </p>
            <?php elseif($name == 'Manage Locations'): ?>
                <p class="mb-0"> Fill in the details below to add a new location to your platform. </p>
            <?php elseif($name == 'Manage Stores'): ?>
                <p class="mb-0"> Enter the store details to expand your retail presence. </p>
            <?php elseif($name == 'Manage Vendors'): ?>
                <p class="mb-0"> Fill in the vendor details to expand your supplier network. </p>
            <?php else: ?>
                <p class="mb-0"> </p>
            <?php endif; ?>

        </div>

        <div class="col-6">
            <?php if((request()->segment(2) == 'family' && request()->segment(3)) == 'create'): ?>
                <div class="d-flex justify-content-end">
                    <a href="<?php echo e(route('attributes.create')); ?>" class="btn btn-primary">Add Attribute</a>
                </div>
            <?php endif; ?>
        </div>

    </div>

</div>
<?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/components/products/add-page-title.blade.php ENDPATH**/ ?>