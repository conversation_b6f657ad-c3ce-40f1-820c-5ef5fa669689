{"__meta": {"id": "01JZ50TJKPP65TGHZV2WN08HVT", "datetime": "2025-07-02 07:32:38", "utime": **********.135065, "method": "GET", "uri": "/auth/google/callback?state=8silp7lma8XK3i2uo1sC036uybugwBqqbPv9tfUC&code=4%2F0AVMBsJiCH9Y2rIINUq0_uYZ21oPi_Vjfdl-AylCD2MQ_1Z0mKHhZGdI6vns0zbjkrX8ukA&scope=email+profile+openid+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.profile+https%3A%2F%2Fwww.googleapis.com%2Fauth%2Fuserinfo.email&authuser=0&hd=apimio.com&prompt=none", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.285835, "end": **********.135085, "duration": 2.849250078201294, "duration_str": "2.85s", "measures": [{"label": "Booting", "start": **********.285835, "relative_start": 0, "end": **********.826199, "relative_end": **********.826199, "duration": 0.****************, "duration_str": "540ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.826216, "relative_start": 0.****************, "end": **********.135087, "relative_end": 1.9073486328125e-06, "duration": 2.***************, "duration_str": "2.31s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.846752, "relative_start": 0.****************, "end": **********.851995, "relative_end": **********.851995, "duration": 0.005243062973022461, "duration_str": "5.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.12998, "relative_start": 2.****************, "end": **********.130872, "relative_end": **********.130872, "duration": 0.0008919239044189453, "duration_str": "892μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "GET auth/google/callback", "middleware": "web, guest", "controller": "App\\Http\\Controllers\\Auth\\RegisterController@handleProviderCallback<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisterController.php&line=184\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisterController.php&line=184\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/RegisterController.php:184-234</a>"}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01132, "accumulated_duration_str": "11.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `email` = '<EMAIL>' and `google_id` is null limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 201}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.083451, "duration": 0.0038799999999999998, "duration_str": "3.88ms", "memory": 0, "memory_str": null, "filename": "RegisterController.php:201", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 201}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisterController.php&line=201", "ajax": false, "filename": "RegisterController.php", "line": "201"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 34.276}, {"sql": "select * from `users` where `email` = '<EMAIL>' limit 1", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 205}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.093289, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "RegisterController.php:205", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 205}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisterController.php&line=205", "ajax": false, "filename": "RegisterController.php", "line": "205"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 34.276, "width_percent": 5.124}, {"sql": "update `users` set `last_login` = '2025-07-02 07:32:38', `users`.`updated_at` = '2025-07-02 07:32:38' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-02 07:32:38", "2025-07-02 07:32:38", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/UserLoginAt.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\UserLoginAt.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 745}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 509}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 207}], "start": **********.104096, "duration": 0.00534, "duration_str": "5.34ms", "memory": 0, "memory_str": null, "filename": "UserLoginAt.php:33", "source": {"index": 15, "namespace": null, "name": "app/Listeners/UserLoginAt.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\UserLoginAt.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FUserLoginAt.php&line=33", "ajax": false, "filename": "UserLoginAt.php", "line": "33"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 39.399, "width_percent": 47.173}, {"sql": "select * from `organization_user` where `user_id` = 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/UserLoginAt.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\UserLoginAt.php", "line": 39}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 745}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 509}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 207}], "start": **********.1154108, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "UserLoginAt.php:39", "source": {"index": 15, "namespace": null, "name": "app/Listeners/UserLoginAt.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\UserLoginAt.php", "line": 39}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FUserLoginAt.php&line=39", "ajax": false, "filename": "UserLoginAt.php", "line": "39"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.572, "width_percent": 6.802}, {"sql": "select * from `team_invites` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Listeners/UserLoginAt.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\UserLoginAt.php", "line": 45}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 745}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 509}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/AuthManager.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Auth/RegisterController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Auth\\RegisterController.php", "line": 207}], "start": **********.119819, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "UserLoginAt.php:45", "source": {"index": 15, "namespace": null, "name": "app/Listeners/UserLoginAt.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Listeners\\UserLoginAt.php", "line": 45}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FListeners%2FUserLoginAt.php&line=45", "ajax": false, "filename": "UserLoginAt.php", "line": "45"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 93.375, "width_percent": 6.625}]}, "models": {"data": {"App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}}, "count": 2, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/google/redirection\"\n]", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01JZ50TEXKRCHAMVCVHCWC58MY\" => null\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1"}, "request": {"data": {"status": "302 Found", "full_url": "http://localhost:8000/auth/google/callback?authuser=0&code=4%2F0AVMBsJiCH9Y2rIINUq0_uYZ21oPi_Vjfdl-A...", "action_name": null, "controller_action": "App\\Http\\Controllers\\Auth\\RegisterController@handleProviderCallback", "uri": "GET auth/google/callback", "controller": "App\\Http\\Controllers\\Auth\\RegisterController@handleProviderCallback<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisterController.php&line=184\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FAuth%2FRegisterController.php&line=184\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Auth/RegisterController.php:184-234</a>", "middleware": "web", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4aed31-2053-4ffe-a59a-e405cccda1a3\" target=\"_blank\">View in Telescope</a>", "duration": "2.85s", "peak_memory": "32MB", "response": "Redirect to http://localhost:8000/home", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-357097210 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>state</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8silp7lma8XK3i2uo1sC036uybugwBqqbPv9tfUC</span>\"\n  \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"73 characters\">4/0AVMBsJiCH9Y2rIINUq0_uYZ21oPi_Vjfdl-AylCD2MQ_1Z0mKHhZGdI6vns0zbjkrX8ukA</span>\"\n  \"<span class=sf-dump-key>scope</span>\" => \"<span class=sf-dump-str title=\"116 characters\">email profile openid https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email</span>\"\n  \"<span class=sf-dump-key>authuser</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>hd</span>\" => \"<span class=sf-dump-str title=\"10 characters\">apimio.com</span>\"\n  \"<span class=sf-dump-key>prompt</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-357097210\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-995536069 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-995536069\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1653151023 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"775 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; XSRF-TOKEN=eyJpdiI6IldtdkVoVlRLQUJJQ2ZTQy9DbC8wc1E9PSIsInZhbHVlIjoiclpOb3daRlFlTmFWNmRJOGkzaG9vSSttM3hITlBtbGoxWkQrV0F2c2c4a3VJaXY5aVZLNVBkWmtwSUVzVUlDN3gyWTBSQXYxaUhlSzJwZlc1dFljNC9Hc3M2S01RY0Zvam1MQW5LZUV5ZUhYQWFpMzZtdFFBQWhUNWlmYUdPTjciLCJtYWMiOiI5YTNkZGM2MDE5Yjc2OTFhYzhhODA3ZmQzY2Q4MTJmYmQ5ZjkyYmE2ZjdjYjBhNTUyY2RiMDk1OTNjNTkyNTY5IiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6Ijlkam5uVVkzRGFHYXZFbE5jbjNvbHc9PSIsInZhbHVlIjoiNnpTTXN1TXpqcWpLaHl0bFhUc3FXTE9RUWtIL093VnE4aDVoQ1hvZXpBRTFmWlBUUnUveHk1ZDFLTlhaY2FlaHRnYTVkV0ZHblBIZCtCMW9XZXNTVHhRY0w5d2c3S253S2N2MGtZaDJVYWJ1cVcxTTdERXFrYXl6eXRveHczRy8iLCJtYWMiOiJkZWVhNmEzODI5ZmYyNTI3M2I5ZDBkODFmYmI0MDUzYWVlNDhkNzNkNjY3ZjE4ODQ5OTk0NmM0MDUwNzJkOTcxIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1653151023\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-466577582 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">sutbD9dauyJhskxMuTq8G6WW49SrogWVlcH3frXa</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-466577582\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-63947260 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 02 Jul 2025 07:32:38 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-63947260\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Gp9FRcKfBhbhwkI78cJmOnCLIUjTetAgHhn0NQEc</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"40 characters\">http://localhost:8000/google/redirection</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JZ50TEXKRCHAMVCVHCWC58MY</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "http://localhost:8000/auth/google/callback?authuser=0&code=4%2F0AVMBsJiCH9Y2rIINUq0_uYZ21oPi_Vjfdl-A...", "controller_action": "App\\Http\\Controllers\\Auth\\RegisterController@handleProviderCallback"}, "badge": "302 Found"}}