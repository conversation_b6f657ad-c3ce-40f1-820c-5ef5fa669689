{"__meta": {"id": "01JZ7FQATDX2Z5T45XA2SR0BJC", "datetime": "2025-07-03 06:31:29", "utime": **********.358472, "method": "GET", "uri": "/products", "ip": "127.0.0.1"}, "php": {"version": "8.2.4", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1751524285.459748, "end": **********.358501, "duration": 3.8987529277801514, "duration_str": "3.9s", "measures": [{"label": "Booting", "start": 1751524285.459748, "relative_start": 0, "end": **********.153756, "relative_end": **********.153756, "duration": 0.****************, "duration_str": "694ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.153783, "relative_start": 0.****************, "end": **********.358504, "relative_end": 3.0994415283203125e-06, "duration": 3.***************, "duration_str": "3.2s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.186935, "relative_start": 0.***************, "end": **********.202352, "relative_end": **********.202352, "duration": 0.015417098999023438, "duration_str": "15.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.91709, "relative_start": 1.****************, "end": **********.355448, "relative_end": **********.355448, "duration": 2.****************, "duration_str": "2.44s", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 7, "nb_templates": 7, "templates": [{"name": "1x products.index", "param_count": null, "params": [], "start": **********.920175, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/products/index.blade.phpproducts.index", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fproducts%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "products.index"}, {"name": "1x components.products.add-product", "param_count": null, "params": [], "start": 1751524287.422016, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/products/add-product.blade.phpcomponents.products.add-product", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fproducts%2Fadd-product.blade.php&line=1", "ajax": false, "filename": "add-product.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.products.add-product"}, {"name": "1x layouts.app_new", "param_count": null, "params": [], "start": 1751524287.585167, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.phplayouts.app_new", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=1", "ajax": false, "filename": "app_new.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.app_new"}, {"name": "1x layouts.navs.product_sidebar", "param_count": null, "params": [], "start": 1751524288.535822, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.phplayouts.navs.product_sidebar", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=1", "ajax": false, "filename": "product_sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.navs.product_sidebar"}, {"name": "1x components.alerts.import-alert", "param_count": null, "params": [], "start": **********.177923, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/import-alert.blade.phpcomponents.alerts.import-alert", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fimport-alert.blade.php&line=1", "ajax": false, "filename": "import-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.import-alert"}, {"name": "1x components.alerts.upgrade-billing-modal", "param_count": null, "params": [], "start": **********.242646, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/alerts/upgrade-billing-modal.blade.phpcomponents.alerts.upgrade-billing-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Falerts%2Fupgrade-billing-modal.blade.php&line=1", "ajax": false, "filename": "upgrade-billing-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.alerts.upgrade-billing-modal"}, {"name": "1x components.assets.delete-modal", "param_count": null, "params": [], "start": **********.302006, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/components/assets/delete-modal.blade.phpcomponents.assets.delete-modal", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Fcomponents%2Fassets%2Fdelete-modal.blade.php&line=1", "ajax": false, "filename": "delete-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "components.assets.delete-modal"}]}, "route": {"uri": "GET products", "middleware": "web, auth, verified, activeOrganization", "controller": "App\\Http\\Controllers\\Product\\ProductController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "as": "products.index", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:51-68</a>"}, "queries": {"count": 58, "nb_statements": 58, "nb_visible_statements": 58, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.05353000000000001, "accumulated_duration_str": "53.53ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.245162, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 0, "width_percent": 7.454}, {"sql": "select * from `notifications` where `notifications`.`notifiable_type` = 'App\\\\User' and `notifications`.`notifiable_id` = 1 and `notifications`.`notifiable_id` is not null and `read_at` is null order by `created_at` desc", "type": "query", "params": [], "bindings": ["App\\User", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\inertiajs\\inertia-laravel\\src\\Middleware.php", "line": 84}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": "middleware", "name": "bindings", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.2602851, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "HandleInertiaRequests.php:43", "source": {"index": 20, "namespace": null, "name": "app/Http/Middleware/HandleInertiaRequests.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Middleware\\HandleInertiaRequests.php", "line": 43}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FMiddleware%2FHandleInertiaRequests.php&line=43", "ajax": false, "filename": "HandleInertiaRequests.php", "line": "43"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 7.454, "width_percent": 1.233}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.342104, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 8.687, "width_percent": 1.345}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.813008, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 10.032, "width_percent": 1.177}, {"sql": "select * from `batch_progress` where `user_id` = 1 and `organization_id` = '1' and `status` = 0 and `organization_id` = '1'", "type": "query", "params": [], "bindings": [1, "1", 0, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 60}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.909764, "duration": 0.00227, "duration_str": "2.27ms", "memory": 0, "memory_str": null, "filename": "ProductController.php:60", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Product/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Http\\Controllers\\Product\\ProductController.php", "line": 60}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=60", "ajax": false, "filename": "ProductController.php", "line": "60"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 11.209, "width_percent": 4.241}, {"sql": "select `id` from `organization_user` where (`user_id` = 1 and `organization_id` = '1') limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 65}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": 1751524287.260138, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:57", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=57", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 15.449, "width_percent": 1.009}, {"sql": "select `id` from `permissions` where `handle` = 'add_and_edit_product' limit 1", "type": "query", "params": [], "bindings": ["add_and_edit_product"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": 1751524287.2884002, "duration": 0.00273, "duration_str": "2.73ms", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:61", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=61", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "61"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 16.458, "width_percent": 5.1}, {"sql": "select * from `organization_user_permissions` where (`organization_user_id` = 1 and `permission_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}], "start": 1751524287.31772, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:71", "source": {"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=71", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "71"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 21.558, "width_percent": 3.755}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.323099, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 25.313, "width_percent": 1.308}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.326911, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 26.621, "width_percent": 1.046}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.333931, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 27.667, "width_percent": 1.27}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.337495, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 28.937, "width_percent": 0.841}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.343621, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 29.778, "width_percent": 1.289}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.34713, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 31.067, "width_percent": 1.252}, {"sql": "select * from `channels` where `organization_id` = '1' and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/View/Components/Products/AddProduct.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\AddProduct.php", "line": 20}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524287.379278, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "AddProduct.php:20", "source": {"index": 15, "namespace": null, "name": "app/View/Components/Products/AddProduct.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\View\\Components\\Products\\AddProduct.php", "line": 20}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FView%2FComponents%2FProducts%2FAddProduct.php&line=20", "ajax": false, "filename": "AddProduct.php", "line": "20"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 32.318, "width_percent": 1.588}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524288.97078, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 33.906, "width_percent": 1.737}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524288.975541, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 35.644, "width_percent": 1.308}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524288.979944, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 36.951, "width_percent": 1.905}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524288.985955, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 38.857, "width_percent": 1.326}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524288.990423, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 40.183, "width_percent": 1.999}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": 1751524288.994442, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 42.182, "width_percent": 0.99}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.000611, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 43.172, "width_percent": 1.588}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.005124, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 44.76, "width_percent": 1.27}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.009815, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 46.03, "width_percent": 1.569}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.014275, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 47.599, "width_percent": 1.7}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.01985, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 49.299, "width_percent": 1.513}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.023622, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 50.813, "width_percent": 1.177}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.030185, "duration": 0.00171, "duration_str": "1.71ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 51.99, "width_percent": 3.194}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.035034, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 55.184, "width_percent": 1.364}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.0394561, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 56.548, "width_percent": 1.494}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.0429552, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 58.042, "width_percent": 1.233}, {"sql": "select `id` from `organization_user` where (`user_id` = 1 and `organization_id` = '1') limit 1", "type": "query", "params": [], "bindings": [1, "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 65}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.048775, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:57", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 57}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=57", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "57"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 59.275, "width_percent": 1.607}, {"sql": "select `id` from `permissions` where `handle` = 'add_and_edit_product' limit 1", "type": "query", "params": [], "bindings": ["add_and_edit_product"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, {"index": 18, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 66}, {"index": 19, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}], "start": **********.052545, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:61", "source": {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=61", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "61"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 60.882, "width_percent": 1.214}, {"sql": "select * from `organization_user_permissions` where (`organization_user_id` = 1 and `permission_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, {"index": 17, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 811}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 764}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 548}], "start": **********.055773, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "OrganizationUserPermissionPolicy.php:71", "source": {"index": 16, "namespace": null, "name": "app/Policies/OrganizationUserPermissionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\OrganizationUserPermissionPolicy.php", "line": 71}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FOrganizationUserPermissionPolicy.php&line=71", "ajax": false, "filename": "OrganizationUserPermissionPolicy.php", "line": "71"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 62.096, "width_percent": 1.345}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.059966, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 63.441, "width_percent": 1.607}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.064325, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 65.048, "width_percent": 1.887}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.069007, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 66.934, "width_percent": 1.644}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.07262, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 68.578, "width_percent": 1.289}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.077182, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 69.867, "width_percent": 1.663}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.081579, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 71.53, "width_percent": 1.438}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.085866, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 72.968, "width_percent": 1.588}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.0895162, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 74.556, "width_percent": 1.158}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.094003, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 75.715, "width_percent": 1.868}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.098165, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 77.583, "width_percent": 1.158}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.102295, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 78.741, "width_percent": 1.681}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.106154, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 80.422, "width_percent": 1.289}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.1109421, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 81.711, "width_percent": 1.607}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.116016, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 83.318, "width_percent": 1.42}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.122321, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 84.738, "width_percent": 2.036}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.126303, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 86.774, "width_percent": 1.121}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.131126, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 87.895, "width_percent": 1.962}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.135036, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 89.856, "width_percent": 1.345}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.139899, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:28", "source": {"index": 20, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 28}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=28", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "28"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 91.201, "width_percent": 1.569}, {"sql": "select * from `subscriptions` where `organization_id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 952}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 795}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Foundation/Application.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php", "line": 986}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Container.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php", "line": 731}], "start": **********.143402, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "SubscriptionPolicy.php:34", "source": {"index": 16, "namespace": null, "name": "app/Policies/SubscriptionPolicy.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\Policies\\SubscriptionPolicy.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FPolicies%2FSubscriptionPolicy.php&line=34", "ajax": false, "filename": "SubscriptionPolicy.php", "line": "34"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 92.77, "width_percent": 1.326}, {"sql": "select * from `organizations` where `id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": ["1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.148381, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "layouts.navs.product_sidebar:435", "source": {"index": 16, "namespace": "view", "name": "layouts.navs.product_sidebar", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/navs/product_sidebar.blade.php", "line": 435}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fnavs%2Fproduct_sidebar.blade.php&line=435", "ajax": false, "filename": "product_sidebar.blade.php", "line": "435"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 94.097, "width_percent": 1.719}, {"sql": "select `organizations`.*, `organization_user`.`user_id` as `pivot_user_id`, `organization_user`.`organization_id` as `pivot_organization_id` from `organizations` inner join `organization_user` on `organizations`.`id` = `organization_user`.`organization_id` where `organization_user`.`user_id` = 1 and `organizations`.`id` = '1' and exists (select * from `users` inner join `organization_user` on `users`.`id` = `organization_user`.`user_id` where `organizations`.`id` = `organization_user`.`organization_id` and `user_id` = 1) limit 1", "type": "query", "params": [], "bindings": [1, "1", 1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.211023, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:249", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 249}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=249", "ajax": false, "filename": "app_new.blade.php", "line": "249"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 95.815, "width_percent": 1.625}, {"sql": "select * from `versions` where `versions`.`organization_id` = 1 and `versions`.`organization_id` is not null", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 72}], "start": **********.2175748, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "layouts.app_new:251", "source": {"index": 16, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 251}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fresources%2Fviews%2Flayouts%2Fapp_new.blade.php&line=251", "ajax": false, "filename": "app_new.blade.php", "line": "251"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 97.441, "width_percent": 1.401}, {"sql": "select count(*) as aggregate from `products` where `organization_id` = '1' and `organization_id` = '1'", "type": "query", "params": [], "bindings": ["1", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, {"index": 17, "namespace": "view", "name": "layouts.app_new", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\resources\\views/layouts/app_new.blade.php", "line": 360}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.3517652, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "User.php:193", "source": {"index": 16, "namespace": null, "name": "app/User.php", "file": "C:\\Users\\<USER>\\Desktop\\work\\apimio\\app\\User.php", "line": 193}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=193", "ajax": false, "filename": "User.php", "line": "193"}, "connection": "a<PERSON><PERSON>", "explain": null, "start_percent": 98.842, "width_percent": 1.158}]}, "models": {"data": {"App\\Models\\Organization\\Organization": {"value": 24, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganization.php&line=1", "ajax": false, "filename": "Organization.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUser": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUser.php&line=1", "ajax": false, "filename": "OrganizationUser.php", "line": "?"}}, "App\\Models\\Organization\\Permission": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\Organization\\OrganizationUserPermission": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FOrganization%2FOrganizationUserPermission.php&line=1", "ajax": false, "filename": "OrganizationUserPermission.php", "line": "?"}}, "App\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Channel\\Channel": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FChannel%2FChannel.php&line=1", "ajax": false, "filename": "Channel.php", "line": "?"}}, "App\\Models\\Product\\Version": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FModels%2FProduct%2FVersion.php&line=1", "ajax": false, "filename": "Version.php", "line": "?"}}}, "count": 33, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 24, "messages": [{"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-347866306 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-347866306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.820425, "xdebug_link": null}, {"message": "[\n  ability => add_and_edit_product,\n  target => App\\Models\\Organization\\OrganizationUserPermission,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Organization\\OrganizationUserPermission, 1 => 1]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1591034317 data-indent-pad=\"  \"><span class=sf-dump-note>add_and_edit_product App\\Models\\Organization\\OrganizationUserPermission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">add_and_edit_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"50 characters\">App\\Models\\Organization\\OrganizationUserPermission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"65 characters\">[0 =&gt; App\\Models\\Organization\\OrganizationUserPermission, 1 =&gt; 1]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1591034317\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524287.322264, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1192712398 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1192712398\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524287.33009, "xdebug_link": null}, {"message": "[\n  ability => create-product,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => [0 => Object(Illuminate\\Database\\Eloquent\\Builder)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-852098815 data-indent-pad=\"  \"><span class=sf-dump-note>create-product </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"14 characters\">create-product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"51 characters\">[0 =&gt; Object(Illuminate\\Database\\Eloquent\\Builder)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-852098815\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524287.340574, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => import,\n  result => true,\n  user => 1,\n  arguments => [0 => import]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1502291623 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess import</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; import]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1502291623\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524287.350596, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-555052574 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-555052574\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524288.978926, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1079474036 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1079474036\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524288.989355, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => gallery,\n  result => true,\n  user => 1,\n  arguments => [0 => gallery]\n]", "message_html": "<pre class=sf-dump id=sf-dump-2010523037 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess gallery</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">gallery</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; gallery]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2010523037\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1751524288.999314, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => invite-team,\n  result => true,\n  user => 1,\n  arguments => [0 => invite-team]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1669811602 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess invite-team</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"11 characters\">invite-team</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"18 characters\">[0 =&gt; invite-team]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1669811602\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.008909, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => notification,\n  result => true,\n  user => 1,\n  arguments => [0 => notification]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1853204963 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess notification</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"12 characters\">notification</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"19 characters\">[0 =&gt; notification]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1853204963\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.018904, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => settings,\n  result => true,\n  user => 1,\n  arguments => [0 => settings]\n]", "message_html": "<pre class=sf-dump id=sf-dump-770317781 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess settings</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">settings</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; settings]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-770317781\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.028378, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => product,\n  result => true,\n  user => 1,\n  arguments => [0 => product]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1451173149 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess product</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"7 characters\">product</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"14 characters\">[0 =&gt; product]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1451173149\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.038507, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => import,\n  result => true,\n  user => 1,\n  arguments => [0 => import]\n]", "message_html": "<pre class=sf-dump id=sf-dump-825164989 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess import</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; import]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-825164989\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.047519, "xdebug_link": null}, {"message": "[\n  ability => add_and_edit_product,\n  target => App\\Models\\Organization\\OrganizationUserPermission,\n  result => true,\n  user => 1,\n  arguments => [0 => App\\Models\\Organization\\OrganizationUserPermission, 1 => 1]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1693268683 data-indent-pad=\"  \"><span class=sf-dump-note>add_and_edit_product App\\Models\\Organization\\OrganizationUserPermission</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"20 characters\">add_and_edit_product</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"50 characters\">App\\Models\\Organization\\OrganizationUserPermission</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"65 characters\">[0 =&gt; App\\Models\\Organization\\OrganizationUserPermission, 1 =&gt; 1]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1693268683\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.05903, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => export,\n  result => true,\n  user => 1,\n  arguments => [0 => export]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1864669681 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess export</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">export</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; export]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1864669681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.068077, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => category,\n  result => true,\n  user => 1,\n  arguments => [0 => category]\n]", "message_html": "<pre class=sf-dump id=sf-dump-504466681 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess category</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">category</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; category]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-504466681\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.076112, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute-set,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute-set]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1242388427 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute-set</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"13 characters\">attribute-set</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"20 characters\">[0 =&gt; attribute-set]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1242388427\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.085016, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => attribute,\n  result => true,\n  user => 1,\n  arguments => [0 => attribute]\n]", "message_html": "<pre class=sf-dump id=sf-dump-585255844 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess attribute</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"9 characters\">attribute</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"16 characters\">[0 =&gt; attribute]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-585255844\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.093007, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => variant-option,\n  result => true,\n  user => 1,\n  arguments => [0 => variant-option]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1592991221 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess variant-option</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"14 characters\">variant-option</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"21 characters\">[0 =&gt; variant-option]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1592991221\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.101357, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => brand,\n  result => true,\n  user => 1,\n  arguments => [0 => brand]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1002443981 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess brand</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">brand</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; brand]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1002443981\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.10991, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => vendor,\n  result => true,\n  user => 1,\n  arguments => [0 => vendor]\n]", "message_html": "<pre class=sf-dump id=sf-dump-375734126 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess vendor</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"6 characters\">vendor</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"13 characters\">[0 =&gt; vendor]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-375734126\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.119423, "xdebug_link": null}, {"message": "[\n  ability => create-lang,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => [0 => Object(Illuminate\\Database\\Eloquent\\Builder)]\n]", "message_html": "<pre class=sf-dump id=sf-dump-198279146 data-indent-pad=\"  \"><span class=sf-dump-note>create-lang </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"11 characters\">create-lang</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"51 characters\">[0 =&gt; Object(Illuminate\\Database\\Eloquent\\Builder)]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-198279146\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.129584, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => location,\n  result => true,\n  user => 1,\n  arguments => [0 => location]\n]", "message_html": "<pre class=sf-dump id=sf-dump-1378501306 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess location</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"8 characters\">location</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"15 characters\">[0 =&gt; location]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378501306\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.138372, "xdebug_link": null}, {"message": "[\n  ability => SubscriptionAccess,\n  target => store,\n  result => true,\n  user => 1,\n  arguments => [0 => store]\n]", "message_html": "<pre class=sf-dump id=sf-dump-586936133 data-indent-pad=\"  \"><span class=sf-dump-note>SubscriptionAccess store</span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"18 characters\">SubscriptionAccess</span>\"\n  \"<span class=sf-dump-key>target</span>\" => \"<span class=sf-dump-str title=\"5 characters\">store</span>\"\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"12 characters\">[0 =&gt; store]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-586936133\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.146996, "xdebug_link": null}]}, "session": {"_token": "9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "_previous": "array:1 [\n  \"url\" => \"http://localhost:8000/api/2024-12/organization/billing/status\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "password_hash_web": "null", "organization_id": "1", "PHPDEBUGBAR_STACK_DATA": "[]", "data": "array:11 [\n  \"input_array\" => array:2 [\n    \"array_name\" => \"CSV\"\n    \"nodes\" => array:1 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:49 [\n          \"Handle\" => \"Handle\"\n          \"Title\" => \"Title\"\n          \"Body (HTML)\" => \"Body (HTML)\"\n          \"Vendor\" => \"Vendor\"\n          \"Type\" => \"Type\"\n          \"Tags\" => \"Tags\"\n          \"Published\" => \"Published\"\n          \"Option1 Name\" => \"Option1 Name\"\n          \"Option1 Value\" => \"Option1 Value\"\n          \"Option2 Name\" => \"Option2 Name\"\n          \"Option2 Value\" => \"Option2 Value\"\n          \"Option3 Name\" => \"Option3 Name\"\n          \"Option3 Value\" => \"Option3 Value\"\n          \"Variant SKU\" => \"Variant SKU\"\n          \"Variant Grams\" => \"Variant Grams\"\n          \"Variant Inventory Tracker\" => \"Variant Inventory Tracker\"\n          \"Variant Inventory Qty\" => \"Variant Inventory Qty\"\n          \"Variant Inventory Policy\" => \"Variant Inventory Policy\"\n          \"Variant Fulfillment Service\" => \"Variant Fulfillment Service\"\n          \"Variant Price\" => \"Variant Price\"\n          \"Variant Compare At Price\" => \"Variant Compare At Price\"\n          \"Variant Requires Shipping\" => \"Variant Requires Shipping\"\n          \"Variant Taxable\" => \"Variant Taxable\"\n          \"Variant Barcode\" => \"Variant Barcode\"\n          \"Image Src\" => \"Image Src\"\n          \"Image Position\" => \"Image Position\"\n          \"Image Alt Text\" => \"Image Alt Text\"\n          \"Gift Card\" => \"Gift Card\"\n          \"SEO Title\" => \"SEO Title\"\n          \"SEO Description\" => \"SEO Description\"\n          \"Google Shopping / Google Product Category\" => \"Google Shopping / Google Product Category\"\n          \"Google Shopping / Gender\" => \"Google Shopping / Gender\"\n          \"Google Shopping / Age Group\" => \"Google Shopping / Age Group\"\n          \"Google Shopping / MPN\" => \"Google Shopping / MPN\"\n          \"Google Shopping / AdWords Grouping\" => \"Google Shopping / AdWords Grouping\"\n          \"Google Shopping / AdWords Labels\" => \"Google Shopping / AdWords Labels\"\n          \"Google Shopping / Condition\" => \"Google Shopping / Condition\"\n          \"Google Shopping / Custom Product\" => \"Google Shopping / Custom Product\"\n          \"Google Shopping / Custom Label 0\" => \"Google Shopping / Custom Label 0\"\n          \"Google Shopping / Custom Label 1\" => \"Google Shopping / Custom Label 1\"\n          \"Google Shopping / Custom Label 2\" => \"Google Shopping / Custom Label 2\"\n          \"Google Shopping / Custom Label 3\" => \"Google Shopping / Custom Label 3\"\n          \"Google Shopping / Custom Label 4\" => \"Google Shopping / Custom Label 4\"\n          \"Variant Image\" => \"Variant Image\"\n          \"Variant Weight Unit\" => \"Variant Weight Unit\"\n          \"Variant Tax Code\" => \"Variant Tax Code\"\n          \"Cost per item\" => \"Cost per item\"\n          \"Status\" => \"Status\"\n          \"List\" => \"List\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_input_array\" => array:1 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:49 [\n        0 => array:2 [\n          \"label\" => \"Handle\"\n          \"value\" => \"Default,Handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Title\"\n          \"value\" => \"Default,Title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Body (HTML)\"\n          \"value\" => \"Default,Body (HTML)\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,Vendor\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Type\"\n          \"value\" => \"Default,Type\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"Default,Tags\"\n        ]\n        6 => array:2 [\n          \"label\" => \"Published\"\n          \"value\" => \"Default,Published\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Option1 Name\"\n          \"value\" => \"Default,Option1 Name\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Option1 Value\"\n          \"value\" => \"Default,Option1 Value\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Option2 Name\"\n          \"value\" => \"Default,Option2 Name\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Option2 Value\"\n          \"value\" => \"Default,Option2 Value\"\n        ]\n        11 => array:2 [\n          \"label\" => \"Option3 Name\"\n          \"value\" => \"Default,Option3 Name\"\n        ]\n        12 => array:2 [\n          \"label\" => \"Option3 Value\"\n          \"value\" => \"Default,Option3 Value\"\n        ]\n        13 => array:2 [\n          \"label\" => \"Variant SKU\"\n          \"value\" => \"Default,Variant SKU\"\n        ]\n        14 => array:2 [\n          \"label\" => \"Variant Grams\"\n          \"value\" => \"Default,Variant Grams\"\n        ]\n        15 => array:2 [\n          \"label\" => \"Variant Inventory Tracker\"\n          \"value\" => \"Default,Variant Inventory Tracker\"\n        ]\n        16 => array:2 [\n          \"label\" => \"Variant Inventory Qty\"\n          \"value\" => \"Default,Variant Inventory Qty\"\n        ]\n        17 => array:2 [\n          \"label\" => \"Variant Inventory Policy\"\n          \"value\" => \"Default,Variant Inventory Policy\"\n        ]\n        18 => array:2 [\n          \"label\" => \"Variant Fulfillment Service\"\n          \"value\" => \"Default,Variant Fulfillment Service\"\n        ]\n        19 => array:2 [\n          \"label\" => \"Variant Price\"\n          \"value\" => \"Default,Variant Price\"\n        ]\n        20 => array:2 [\n          \"label\" => \"Variant Compare At Price\"\n          \"value\" => \"Default,Variant Compare At Price\"\n        ]\n        21 => array:2 [\n          \"label\" => \"Variant Requires Shipping\"\n          \"value\" => \"Default,Variant Requires Shipping\"\n        ]\n        22 => array:2 [\n          \"label\" => \"Variant Taxable\"\n          \"value\" => \"Default,Variant Taxable\"\n        ]\n        23 => array:2 [\n          \"label\" => \"Variant Barcode\"\n          \"value\" => \"Default,Variant Barcode\"\n        ]\n        24 => array:2 [\n          \"label\" => \"Image Src\"\n          \"value\" => \"Default,Image Src\"\n        ]\n        25 => array:2 [\n          \"label\" => \"Image Position\"\n          \"value\" => \"Default,Image Position\"\n        ]\n        26 => array:2 [\n          \"label\" => \"Image Alt Text\"\n          \"value\" => \"Default,Image Alt Text\"\n        ]\n        27 => array:2 [\n          \"label\" => \"Gift Card\"\n          \"value\" => \"Default,Gift Card\"\n        ]\n        28 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"Default,SEO Title\"\n        ]\n        29 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"Default,SEO Description\"\n        ]\n        30 => array:2 [\n          \"label\" => \"Google Shopping / Google Product Category\"\n          \"value\" => \"Default,Google Shopping / Google Product Category\"\n        ]\n        31 => array:2 [\n          \"label\" => \"Google Shopping / Gender\"\n          \"value\" => \"Default,Google Shopping / Gender\"\n        ]\n        32 => array:2 [\n          \"label\" => \"Google Shopping / Age Group\"\n          \"value\" => \"Default,Google Shopping / Age Group\"\n        ]\n        33 => array:2 [\n          \"label\" => \"Google Shopping / MPN\"\n          \"value\" => \"Default,Google Shopping / MPN\"\n        ]\n        34 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Grouping\"\n          \"value\" => \"Default,Google Shopping / AdWords Grouping\"\n        ]\n        35 => array:2 [\n          \"label\" => \"Google Shopping / AdWords Labels\"\n          \"value\" => \"Default,Google Shopping / AdWords Labels\"\n        ]\n        36 => array:2 [\n          \"label\" => \"Google Shopping / Condition\"\n          \"value\" => \"Default,Google Shopping / Condition\"\n        ]\n        37 => array:2 [\n          \"label\" => \"Google Shopping / Custom Product\"\n          \"value\" => \"Default,Google Shopping / Custom Product\"\n        ]\n        38 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 0\"\n          \"value\" => \"Default,Google Shopping / Custom Label 0\"\n        ]\n        39 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 1\"\n          \"value\" => \"Default,Google Shopping / Custom Label 1\"\n        ]\n        40 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 2\"\n          \"value\" => \"Default,Google Shopping / Custom Label 2\"\n        ]\n        41 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 3\"\n          \"value\" => \"Default,Google Shopping / Custom Label 3\"\n        ]\n        42 => array:2 [\n          \"label\" => \"Google Shopping / Custom Label 4\"\n          \"value\" => \"Default,Google Shopping / Custom Label 4\"\n        ]\n        43 => array:2 [\n          \"label\" => \"Variant Image\"\n          \"value\" => \"Default,Variant Image\"\n        ]\n        44 => array:2 [\n          \"label\" => \"Variant Weight Unit\"\n          \"value\" => \"Default,Variant Weight Unit\"\n        ]\n        45 => array:2 [\n          \"label\" => \"Variant Tax Code\"\n          \"value\" => \"Default,Variant Tax Code\"\n        ]\n        46 => array:2 [\n          \"label\" => \"Cost per item\"\n          \"value\" => \"Default,Cost per item\"\n        ]\n        47 => array:2 [\n          \"label\" => \"Status\"\n          \"value\" => \"Default,Status\"\n        ]\n        48 => array:2 [\n          \"label\" => \"List\"\n          \"value\" => \"Default,List\"\n        ]\n      ]\n    ]\n  ]\n  \"file_path\" => \"mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"file_url\" => \"https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv\"\n  \"data_required\" => array:8 [\n    \"template_method_type\" => \"import\"\n    \"output_type\" => \"Apimio\"\n    \"sync\" => false\n    \"redirect_url_route\" => \"products.import\"\n    \"organization_id\" => \"1\"\n    \"versions\" => array:1 [\n      1 => \"EN-US\"\n    ]\n    \"catalogs\" => array:1 [\n      1 => \"Tanzayb Store\"\n    ]\n    \"import_action\" => \"3\"\n  ]\n  \"import_action\" => \"3\"\n  \"apimio_attributes_required\" => array:2 [\n    \"all_families\" => []\n    \"all_attributes\" => array:6 [\n      0 => array:2 [\n        \"id\" => 1\n        \"name\" => \"single line text\"\n      ]\n      1 => array:2 [\n        \"id\" => 2\n        \"name\" => \"number\"\n      ]\n      2 => array:2 [\n        \"id\" => 3\n        \"name\" => \"multi line text\"\n      ]\n      3 => array:2 [\n        \"id\" => 7\n        \"name\" => \"measurement\"\n      ]\n      4 => array:2 [\n        \"id\" => 9\n        \"name\" => \"json\"\n      ]\n      5 => array:2 [\n        \"id\" => 11\n        \"name\" => \"url\"\n      ]\n    ]\n  ]\n  \"template_attributes\" => []\n  \"output_array\" => array:2 [\n    \"array_name\" => \"Apimio\"\n    \"nodes\" => array:6 [\n      0 => array:2 [\n        \"name\" => \"Default\"\n        \"attributes\" => array:5 [\n          \"handle\" => \"Product Identifier\"\n          \"file\" => \"Product Images\"\n          \"vendor\" => \"Vendor\"\n          \"brand\" => \"Brand\"\n          \"categories\" => \"Category\"\n        ]\n      ]\n      1 => array:2 [\n        \"name\" => \"General\"\n        \"attributes\" => array:2 [\n          \"product_name\" => \"Product Name\"\n          \"description\" => \"Description\"\n        ]\n      ]\n      2 => array:2 [\n        \"name\" => \"Variant\"\n        \"attributes\" => array:11 [\n          \"sku\" => \"SKU\"\n          \"name\" => \"Name\"\n          \"file\" => \"Image\"\n          \"price\" => \"Price\"\n          \"compare_at_price\" => \"Compare at Price\"\n          \"cost_price\" => \"Cost Price\"\n          \"barcode\" => \"UPC / Barcode\"\n          \"weight\" => \"Weight\"\n          \"weight_unit\" => \"Weight Unit\"\n          \"track_quantity\" => \"Track Quantity\"\n          \"continue_selling\" => \"Continue Selling\"\n        ]\n      ]\n      3 => array:2 [\n        \"name\" => \"Variant Option\"\n        \"attributes\" => array:6 [\n          \"option1_name\" => \"Option 1 Name\"\n          \"option1_value\" => \"Option 1 Value\"\n          \"option2_name\" => \"Option 2 Name\"\n          \"option2_value\" => \"Option 2 Value\"\n          \"option3_name\" => \"Option 3 Name\"\n          \"option3_value\" => \"Option 3 Value\"\n        ]\n      ]\n      4 => array:2 [\n        \"name\" => \"Inventory -> Tanzayb Store\"\n        \"attributes\" => array:1 [\n          1 => \"Tanzayb Store Warehouse\"\n        ]\n      ]\n      5 => array:2 [\n        \"name\" => \"SEO\"\n        \"attributes\" => array:4 [\n          \"seo_url\" => \"URL Slug\"\n          \"seo_title\" => \"SEO Title\"\n          \"seo_description\" => \"SEO Description\"\n          \"seo_keyword\" => \"Tags\"\n        ]\n      ]\n    ]\n  ]\n  \"converted_output_array\" => array:6 [\n    0 => array:3 [\n      \"label\" => \"Default\"\n      \"title\" => \"Default\"\n      \"options\" => array:5 [\n        0 => array:2 [\n          \"label\" => \"Product Identifier\"\n          \"value\" => \"Default,handle\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Product Images\"\n          \"value\" => \"Default,file\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Vendor\"\n          \"value\" => \"Default,vendor\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Brand\"\n          \"value\" => \"Default,brand\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Category\"\n          \"value\" => \"Default,categories\"\n        ]\n      ]\n    ]\n    1 => array:3 [\n      \"label\" => \"General\"\n      \"title\" => \"General\"\n      \"options\" => array:2 [\n        0 => array:2 [\n          \"label\" => \"Product Name\"\n          \"value\" => \"General,product_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Description\"\n          \"value\" => \"General,description\"\n        ]\n      ]\n    ]\n    2 => array:3 [\n      \"label\" => \"Variant\"\n      \"title\" => \"Variant\"\n      \"options\" => array:11 [\n        0 => array:2 [\n          \"label\" => \"SKU\"\n          \"value\" => \"Variant,sku\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Name\"\n          \"value\" => \"Variant,name\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Image\"\n          \"value\" => \"Variant,file\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Price\"\n          \"value\" => \"Variant,price\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Compare at Price\"\n          \"value\" => \"Variant,compare_at_price\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Cost Price\"\n          \"value\" => \"Variant,cost_price\"\n        ]\n        6 => array:2 [\n          \"label\" => \"UPC / Barcode\"\n          \"value\" => \"Variant,barcode\"\n        ]\n        7 => array:2 [\n          \"label\" => \"Weight\"\n          \"value\" => \"Variant,weight\"\n        ]\n        8 => array:2 [\n          \"label\" => \"Weight Unit\"\n          \"value\" => \"Variant,weight_unit\"\n        ]\n        9 => array:2 [\n          \"label\" => \"Track Quantity\"\n          \"value\" => \"Variant,track_quantity\"\n        ]\n        10 => array:2 [\n          \"label\" => \"Continue Selling\"\n          \"value\" => \"Variant,continue_selling\"\n        ]\n      ]\n    ]\n    3 => array:3 [\n      \"label\" => \"Variant Option\"\n      \"title\" => \"Variant Option\"\n      \"options\" => array:6 [\n        0 => array:2 [\n          \"label\" => \"Option 1 Name\"\n          \"value\" => \"Variant Option,option1_name\"\n        ]\n        1 => array:2 [\n          \"label\" => \"Option 1 Value\"\n          \"value\" => \"Variant Option,option1_value\"\n        ]\n        2 => array:2 [\n          \"label\" => \"Option 2 Name\"\n          \"value\" => \"Variant Option,option2_name\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Option 2 Value\"\n          \"value\" => \"Variant Option,option2_value\"\n        ]\n        4 => array:2 [\n          \"label\" => \"Option 3 Name\"\n          \"value\" => \"Variant Option,option3_name\"\n        ]\n        5 => array:2 [\n          \"label\" => \"Option 3 Value\"\n          \"value\" => \"Variant Option,option3_value\"\n        ]\n      ]\n    ]\n    4 => array:3 [\n      \"label\" => \"Inventory -> Tanzayb Store\"\n      \"title\" => \"Inventory -> Tanzayb Store\"\n      \"options\" => array:1 [\n        0 => array:2 [\n          \"label\" => \"Tanzayb Store Warehouse\"\n          \"value\" => \"Inventory -> Tanzayb Store,1\"\n        ]\n      ]\n    ]\n    5 => array:3 [\n      \"label\" => \"SEO\"\n      \"title\" => \"SEO\"\n      \"options\" => array:4 [\n        0 => array:2 [\n          \"label\" => \"URL Slug\"\n          \"value\" => \"SEO,seo_url\"\n        ]\n        1 => array:2 [\n          \"label\" => \"SEO Title\"\n          \"value\" => \"SEO,seo_title\"\n        ]\n        2 => array:2 [\n          \"label\" => \"SEO Description\"\n          \"value\" => \"SEO,seo_description\"\n        ]\n        3 => array:2 [\n          \"label\" => \"Tags\"\n          \"value\" => \"SEO,seo_keyword\"\n        ]\n      ]\n    ]\n  ]\n  \"mapping_data\" => array:49 [\n    0 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Handle\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,handle\"\n      ]\n    ]\n    1 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    2 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Body (HTML)\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    3 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Vendor\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"Default,vendor\"\n      ]\n    ]\n    4 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Type\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    5 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Tags\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_keyword\"\n      ]\n    ]\n    6 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Published\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    7 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    8 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option1 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    9 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    10 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option2 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    11 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Name\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    12 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Option3 Value\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    13 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant SKU\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    14 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Grams\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    15 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Tracker\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    16 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Qty\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    17 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Inventory Policy\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    18 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Fulfillment Service\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    19 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    20 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Compare At Price\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    21 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Requires Shipping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    22 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Taxable\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    23 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Barcode\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    24 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Src\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    25 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Position\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    26 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Image Alt Text\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    27 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Gift Card\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    28 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Title\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_title\"\n      ]\n    ]\n    29 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,SEO Description\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => array:1 [\n        0 => \"SEO,seo_description\"\n      ]\n    ]\n    30 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Google Product Category\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    31 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Gender\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    32 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Age Group\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    33 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / MPN\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    34 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Grouping\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    35 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / AdWords Labels\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    36 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Condition\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    37 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Product\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    38 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 0\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    39 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 1\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    40 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 2\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    41 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 3\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    42 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Google Shopping / Custom Label 4\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    43 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Image\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    44 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Weight Unit\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    45 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Variant Tax Code\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    46 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Cost per item\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    47 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,Status\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n    48 => array:3 [\n      \"from\" => array:1 [\n        0 => \"Default,List\"\n      ]\n      \"with_formula\" => \"assign\"\n      \"to\" => []\n    ]\n  ]\n]"}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/products", "action_name": "products.index", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@index", "uri": "GET products", "controller": "App\\Http\\Controllers\\Product\\ProductController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "App\\Http\\Controllers", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2FApimio2%2FDesktop%2Fwork%2Fapimio%2Fapp%2FHttp%2FControllers%2FProduct%2FProductController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Product/ProductController.php:51-68</a>", "middleware": "web", "telescope": "<a href=\"http://localhost:8000/_debugbar/telescope/9f4cda4e-ffcb-4070-8e56-23506a8ef1e0\" target=\"_blank\">View in Telescope</a>", "duration": "3.92s", "peak_memory": "38MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1806263657 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1806263657\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-7839740 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-7839740\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-661375995 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">http://localhost:8000/products/import/step1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1205 characters\">__stripe_mid=5fa877f3-5d7b-4255-b2c2-ced2f9ae1950465a52; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IkVRWTIyWSs4OCs4d2QyKy9Bc2xMWVE9PSIsInZhbHVlIjoiU1VHWEZSZ21ERUxuOWhrODBFd1FHblFHam1JMmhLckpTTVU4bFBqTlgyVTUzYXlBaGhRU0R4RlBrc2Q0YWE3d0tpQUhhWGNaRG1yRTZRM2tNRFIyY0dLWkxjT0tPME9GMjQyU2lnck1CTEJ2OTV4NlE1cXBMMUVid1J5VzYvenBIUXV1Y2NLL1krZno4WkM4ZnFSaXl3PT0iLCJtYWMiOiJhZGU2ZmY5MzgwYTQ4MmVhMmQ1ODg2MDY2NTMyYjc4NmMyNDUwN2Y1ZmYyZGZmZmExZmI2ZWZhNzdkMWY4M2FiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InM1MDdKS2ZzcHFFcm1nblpIaVc5SWc9PSIsInZhbHVlIjoiSisyY2hoWXBtREJwdjlDZHRmQWFHTStTMTF5c2FWTndIcThWcFVqWEJ3K2ZrSTFkODY1N2hBYlhHSldRV0hJTnVpRjB5Tm9yNGo0dUdvaW9FMFlKLy9iWTdaS2ZHWmphcEsxazA0NDc5VU5ZTmVLWmVGeTZtQ1JRN0VISXBOUzEiLCJtYWMiOiJiNzBhODM0YWRiODJlNGJlNWIxZjI0NzNmMzcwNTkwMzViMmYyMzliZDM2MzQyZDM1MzYxYmE3MTk1ZGRhOGFiIiwidGFnIjoiIn0%3D; apimio_local_session=eyJpdiI6Ik1FRmpvUEdseE9ZNnZzQmhPdzJuV2c9PSIsInZhbHVlIjoiMTdyZHJ1OWVwaTdoRlRhYmlMclAzUUZwZCtlVUxwRUZFUGpvUUtYZFUzSWpjd0RCWllKaENmUThxbEZ6VldTeUt3dVpWdGx2Ui8yZjkvYzZzRkp0QXZFWnRTYmtzU0dFSFR0MW5iek1OckkvTXhZMS92THlJVi9SYmhGZ2ltT1giLCJtYWMiOiJjYzdhODYzNmEzOWM3ZmM4YzU2MGQwNzYwZjBjYzAyOGI1NjRmNmJkZGIyOTQ5YThhNmJjNWIzNTEyMDQxYmQyIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-661375995\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-892120139 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>__stripe_mid</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"63 characters\">1|w18UHrcfC6aDKtbZiXNufqtJTAKIroQoSW6TxcJOAUYCh5QhrhhnAegAY6Pz|</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>apimio_local_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Hpj02iYobWSFRbp0Zjz10UyQzMp42LbVgb86a16Q</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892120139\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-359251147 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 03 Jul 2025 06:31:26 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-359251147\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2049146571 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">9RKOAZ4achVOh7j0iSV3kNrcdWTwWnKKcnm6hzv9</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"61 characters\">http://localhost:8000/api/2024-12/organization/billing/status</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>password_hash_web</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>input_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">CSV</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>Handle</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>Title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>Body (HTML)</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>Vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>Type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>Tags</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>Published</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>Option1 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>Option1 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>Option2 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>Option2 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>Option3 Name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>Option3 Value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>Variant SKU</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>Variant Grams</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Tracker</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Qty</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>Variant Inventory Policy</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>Variant Fulfillment Service</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>Variant Price</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>Variant Compare At Price</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>Variant Requires Shipping</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>Variant Taxable</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>Variant Barcode</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>Image Src</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>Image Position</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>Image Alt Text</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>Gift Card</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>SEO Title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>SEO Description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Google Product Category</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Gender</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Age Group</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>Google Shopping / MPN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Grouping</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>Google Shopping / AdWords Labels</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Condition</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Product</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 0</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 1</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 2</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 3</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>Google Shopping / Custom Label 4</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>Variant Image</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>Variant Weight Unit</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>Variant Tax Code</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>Cost per item</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>Status</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>List</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_input_array</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Handle</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Body (HTML)</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Type</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Published</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Option3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n          </samp>]\n          <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n          </samp>]\n          <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n          </samp>]\n          <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Grams</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n          </samp>]\n          <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Inventory Tracker</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n          </samp>]\n          <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Variant Inventory Qty</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n          </samp>]\n          <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Inventory Policy</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n          </samp>]\n          <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Fulfillment Service</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n          </samp>]\n          <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant Compare At Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n          </samp>]\n          <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Variant Requires Shipping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n          </samp>]\n          <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Taxable</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n          </samp>]\n          <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Image Src</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n          </samp>]\n          <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Position</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n          </samp>]\n          <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Image Alt Text</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n          </samp>]\n          <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Gift Card</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n          </samp>]\n          <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n          </samp>]\n          <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n          </samp>]\n          <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"41 characters\">Google Shopping / Google Product Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n          </samp>]\n          <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Google Shopping / Gender</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n          </samp>]\n          <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Age Group</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n          </samp>]\n          <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Google Shopping / MPN</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n          </samp>]\n          <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Google Shopping / AdWords Grouping</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n          </samp>]\n          <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / AdWords Labels</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n          </samp>]\n          <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Google Shopping / Condition</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n          </samp>]\n          <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Product</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n          </samp>]\n          <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 0</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n          </samp>]\n          <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 1</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n          </samp>]\n          <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 2</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n          </samp>]\n          <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 3</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n          </samp>]\n          <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Google Shopping / Custom Label 4</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n          </samp>]\n          <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n          </samp>]\n          <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Variant Tax Code</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n          </samp>]\n          <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Cost per item</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n          </samp>]\n          <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Status</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n          </samp>]\n          <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">List</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>file_path</span>\" => \"<span class=sf-dump-str title=\"50 characters\">mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>file_url</span>\" => \"<span class=sf-dump-str title=\"100 characters\">https://apimio-staging.s3.us-east-2.amazonaws.com/mapping_fields/upload/json/1751523467_datafile.csv</span>\"\n    \"<span class=sf-dump-key>data_required</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>template_method_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">import</span>\"\n      \"<span class=sf-dump-key>output_type</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>sync</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>redirect_url_route</span>\" => \"<span class=sf-dump-str title=\"15 characters\">products.import</span>\"\n      \"<span class=sf-dump-key>organization_id</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>versions</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"5 characters\">EN-US</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>catalogs</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"13 characters\">Tanzayb Store</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>import_action</span>\" => \"<span class=sf-dump-str>3</span>\"\n    \"<span class=sf-dump-key>apimio_attributes_required</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>all_families</span>\" => []\n      \"<span class=sf-dump-key>all_attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"16 characters\">single line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">number</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"15 characters\">multi line text</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">measurement</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">json</span>\"\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">url</span>\"\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>template_attributes</span>\" => []\n    \"<span class=sf-dump-key>output_array</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>array_name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Apimio</span>\"\n      \"<span class=sf-dump-key>nodes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>handle</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>vendor</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>brand</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>categories</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>product_name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>sku</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>price</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>compare_at_price</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>cost_price</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>barcode</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>weight</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>weight_unit</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>track_quantity</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>continue_selling</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>option1_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>option1_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>option2_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>option2_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>option3_name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>option3_value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-key>1</span> => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n          </samp>]\n        </samp>]\n        <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n          \"<span class=sf-dump-key>attributes</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>seo_url</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>seo_title</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>seo_description</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>seo_keyword</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>converted_output_array</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Default</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Product Identifier</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Product Images</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Default,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Vendor</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Brand</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Default,brand</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Category</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Default,categories</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">General</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Product Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"20 characters\">General,product_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">General,description</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Variant</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SKU</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Variant,sku</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,name</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Image</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Variant,file</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Variant,price</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Compare at Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,compare_at_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Cost Price</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Variant,cost_price</span>\"\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">UPC / Barcode</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">Variant,barcode</span>\"\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Weight</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant,weight</span>\"\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Weight Unit</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Variant,weight_unit</span>\"\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Track Quantity</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Variant,track_quantity</span>\"\n          </samp>]\n          <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"16 characters\">Continue Selling</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Variant,continue_selling</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Variant Option</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 1 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option1_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 1 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option1_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 2 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option2_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 2 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option2_value</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Option 3 Name</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"27 characters\">Variant Option,option3_name</span>\"\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"14 characters\">Option 3 Value</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Variant Option,option3_value</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Inventory -&gt; Tanzayb Store</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"23 characters\">Tanzayb Store Warehouse</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Inventory -&gt; Tanzayb Store,1</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">SEO</span>\"\n        \"<span class=sf-dump-key>options</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"8 characters\">URL Slug</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"11 characters\">SEO,seo_url</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"9 characters\">SEO Title</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO Description</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>label</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Tags</span>\"\n            \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>mapping_data</span>\" => <span class=sf-dump-note>array:49</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Handle</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,handle</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">Default,Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Body (HTML)</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Vendor</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,vendor</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Type</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,Tags</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">SEO,seo_keyword</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Published</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option1 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option1 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option2 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option2 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">Default,Option3 Name</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Option3 Value</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">Default,Variant SKU</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Grams</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Inventory Tracker</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Variant Inventory Qty</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Inventory Policy</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Variant Fulfillment Service</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Variant Compare At Price</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">Default,Variant Requires Shipping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Taxable</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,Variant Barcode</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Image Src</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Position</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">Default,Image Alt Text</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,Gift Card</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">Default,SEO Title</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">SEO,seo_title</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">Default,SEO Description</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"19 characters\">SEO,seo_description</span>\"\n        </samp>]\n      </samp>]\n      <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"49 characters\">Default,Google Shopping / Google Product Category</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">Default,Google Shopping / Gender</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Age Group</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Default,Google Shopping / MPN</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">Default,Google Shopping / AdWords Grouping</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / AdWords Labels</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">Default,Google Shopping / Condition</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Product</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 0</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 1</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 2</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 3</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">Default,Google Shopping / Custom Label 4</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Variant Image</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">Default,Variant Weight Unit</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">Default,Variant Tax Code</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">Default,Cost per item</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">Default,Status</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n      <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>from</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">Default,List</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>with_formula</span>\" => \"<span class=sf-dump-str title=\"6 characters\">assign</span>\"\n        \"<span class=sf-dump-key>to</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2049146571\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/products", "action_name": "products.index", "controller_action": "App\\Http\\Controllers\\Product\\ProductController@index"}, "badge": null}}