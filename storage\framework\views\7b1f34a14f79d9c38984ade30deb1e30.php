

<?php $__env->startSection('titles','Vendors'); ?>
<?php $__env->startSection('content'); ?>

    <div>
        <?php if (isset($component)) { $__componentOriginal262b0e1ee858dd683d724746646aea00 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal262b0e1ee858dd683d724746646aea00 = $attributes; } ?>
<?php $component = App\View\Components\Products\PageTitle::resolve(['name' => ''.e(trans('products_vendors.page_title')).'','description' => ''.e(trans('products_vendors.page_description')).'','links' => 'true','button' => 'true'] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('products.page-title'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\Products\PageTitle::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
             <?php $__env->slot('addbutton', null, []); ?> 
                <a href="<?php echo e(route('vendors.create')); ?>" style="width: 194px" id="add-vendors"
                   class="btn btn-primary ripplelink
                               float-lg-right float-md-right "><?php echo e(trans('products_vendors.add_vendor_btn')); ?></a>
             <?php $__env->endSlot(); ?>
         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $attributes = $__attributesOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__attributesOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal262b0e1ee858dd683d724746646aea00)): ?>
<?php $component = $__componentOriginal262b0e1ee858dd683d724746646aea00; ?>
<?php unset($__componentOriginal262b0e1ee858dd683d724746646aea00); ?>
<?php endif; ?>
        <div class="row">
            <div class="col-12 col-md-12 col-lg-12 col-xl-3">
                <?php if(count($data["vendor"]) > 0): ?>
                    <?php if (isset($component)) { $__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142 = $attributes; } ?>
<?php $component = App\View\Components\General\SearchBar::resolve(['placeholder' => ''.e(trans('products_vendors.search_placeholder')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.search-bar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\SearchBar::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142)): ?>
<?php $attributes = $__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142; ?>
<?php unset($__attributesOriginal7f4a0b6e888c5b135a130c3e4faf3142); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142)): ?>
<?php $component = $__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142; ?>
<?php unset($__componentOriginal7f4a0b6e888c5b135a130c3e4faf3142); ?>
<?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
        <div >
            <div >
                <div class="row">
                    <div>
                        <?php if(count($data['vendor']) > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-borderless">
                                    <caption style="visibility: hidden"></caption>
                                    <thead class="thead-light" style="height: 50px;">
                                    <tr>
                                        <th scope="col" class="Roboto bold text-dark w-25 border-radius-right border-radius-left">
                                            <?php echo e(__('Name')); ?>

                                        </th>
                                        <th class="Roboto bold text-dark disabled-sorting text-end w-25 border-radius-right border-radius-left"><?php echo e(__('Actions')); ?>

                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $data['vendor']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $vendor): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td style="padding: 0.75rem!important;"
                                                class="Roboto regular mt-3 text-left">
                                                <?php echo e(__(substr($vendor->fname,0,52))); ?>

                                            </td>

                                            <td class="Roboto mt-3 text-end">
                                                <a href="<?php echo e(route('vendors.edit', $vendor->id)); ?>"
                                                   class="pro_ven_edit mr-3">
                                                   <i class="fa-regular fa-pen-to-square fs-20" aria-hidden="true"></i>

                                                </a>

                                                <a href="#" data-id="<?php echo e($vendor->id); ?>" data-retailer-name=""
                                                   data-bs-toggle="modal" data-bs-target="#delete-modal" class="btn-delete">
                                                    <i class="fa-regular fa-trash-can fs-20 text-danger" aria-hidden="true"></i>

                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </tbody>
                                </table>
                                <?php echo $data['vendor']->appends($request->all())->links(); ?>

                            </div>
                        <?php else: ?>
                            <?php if (isset($component)) { $__componentOriginal9c590450895f60ce3af8182e3a1a843e = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9c590450895f60ce3af8182e3a1a843e = $attributes; } ?>
<?php $component = App\View\Components\General\EmptyPage::resolve(['description' => ''.e(trans('products_vendors.page_empty')).''] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('general.empty-page'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\General\EmptyPage::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $attributes = $__attributesOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__attributesOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9c590450895f60ce3af8182e3a1a843e)): ?>
<?php $component = $__componentOriginal9c590450895f60ce3af8182e3a1a843e; ?>
<?php unset($__componentOriginal9c590450895f60ce3af8182e3a1a843e); ?>
<?php endif; ?>
                        <?php endif; ?>
                    </div>

                </div>
            </div>
        </div>
    </div>

    <!-- Modal DELETE-->
    <div class="modal fade" id="delete-modal" tabindex="-1" aria-labelledby="deleteModalLabel"
         aria-hidden="true" >
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title Poppins semibold" id="exampleModalLabel"><?php echo e(trans('products_vendors.modal_title')); ?></h5>
                    <button type="button" class="close" data-bs-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p class="Roboto regular"><?php echo e(trans('products_vendors.modal_description')); ?>


                    </p>

                    <div class="modal-footer p-0">
                        <button type="button" data-bs-dismiss="modal" id="delete-cancel-btn"
                                class="btn btn-dark-tertiary float-left shadow"
                                style="width: 120px;">
                            <?php echo e(trans('products_vendors.cancel_btn')); ?>

                        </button>
                        <form action="#" id="delete-vendor" method="post">
                            <?php echo csrf_field(); ?>
                            <?php echo method_field('DELETE'); ?>
                            <button id="pro_ven_del_btn" class="btn btn-danger ripplelink shadow"
                                    onclick="del()"><?php echo e(trans('products_vendors.delete_btn')); ?></button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('footer_scripts'); ?>
    <script type="text/javascript">
        let attrid;
        let attrname;

        $(".btn-delete").click(function () {
            attrid = $(this).attr('data-id');
            attrname = $(this).attr('data-retailer-name');
            document.getElementById('name').innerHTML = attrname;
        });

        function del() {
            var form = document.getElementById('delete-vendor');
            form.setAttribute('action', 'vendors/' + attrid);
            form.submit();
        }
    </script>

    <script type="text/javascript">
        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        $('#add-modal').modal('show')
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app_new', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\work\apimio\resources\views/products/vendors/view.blade.php ENDPATH**/ ?>